package com.shands.mod.external.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 */
@ApiModel(value = "二维码参数")
@Data
public class QRCodeDto {

  // 员工手机号
  private String m;

  // 员工ucId
  private String u;

  private String h;

  // 注册来源：百达星还是商祺会
  private String s = "BDW";

  // 渠道
  private String c = "H-001-01";

  /**
   * Q 表示走企业会员注册流程;
   * S 表示销售助手订单分享行程;
   */
  private String t;

  private int from = 0; // from : 0是其他；1是剧本杀

  private int fromFeiShu;

  private Long teamId;

  // qrType:0，加入百达屋；1，加入企业会员；2，会场海报
  public Integer qrType;

  // pageId，会场海报的海报id
  public String pageId;

  /** 销售助手 **/
  @ApiModelProperty(value = "主订单号")
  private String orderMainNo;

  @ApiModelProperty("事件类型")
  private String eventType;

  @ApiModelProperty(name = "销售id")
  private String salesId;
  /** 销售助手 **/

  private String hotelCode;

  @ApiModelProperty("事件id")
  private String eventId;

}
