package com.shands.mod.external.service.impl;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shands.mod.dao.model.req.DevelopmentMembershipReq;
import com.shands.mod.dao.model.req.GiftOrderBo;
import com.shands.mod.external.model.vo.ElsResponse;
import com.shands.mod.external.service.ElsMemberCenterService;
import com.shands.mod.external.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ElsMemberCenterServiceImpl implements ElsMemberCenterService {

  @Value("${app.gw.appSecret}")
  private String appSecret;
  @Value("${app.gw.appKey}")
  private String appKey;
  @Value("${app.gw.host}")
  private String domain;

  /**
   * 获取发展会员信息
   */
  @Override
  public ElsResponse getDevelopmentMember(DevelopmentMembershipReq developmentMembershipReq) {
    String api = "/els-member-center/api/toMod/getDevelopmentMember";
    String send = this
        .send(JSON.toJSONString(developmentMembershipReq), api, domain, appKey, appSecret);
    ElsResponse gwPageResponse = JSON
        .parseObject(send, new TypeReference<ElsResponse>() {
        });
    return gwPageResponse;
  }

  /**
   * 获取礼包订单记录接口
   *
   * @param giftOrderReq
   */
  @Override
  public ElsResponse getGiftPackOrder(GiftOrderBo giftOrderReq) {
    String api = "/els-member-center/api/toMod/getGiftOrderDetail";

    String send = this.send(JSON.toJSONString(giftOrderReq), api, domain, appKey, appSecret);
    ElsResponse gwPageResponse = JSON
        .parseObject(send, new TypeReference<ElsResponse>() {
        });
    return gwPageResponse;
  }

  private String send(String body, String api, String domain, String appKey, String appSecret) {
    String timestamp = String.valueOf(System.currentTimeMillis());
    String sign = getSign(body, timestamp, appSecret, appKey);
    StringBuilder url = new StringBuilder(domain).append(api);
    url.append("?appkey=").append(appKey);
    url.append("&timestamp=").append(timestamp);
    url.append("&token=").append(sign);
    HttpRequest httpRequest = HttpUtil.createPost(url.toString());
    httpRequest.header(Header.ACCEPT, "*/*");
    httpRequest.header(Header.CONTENT_TYPE, "application/json");
    String res = httpRequest.body(body).execute().body();
    return res;
  }

  private String getSign(String body, String timestamp, String appSecret, String appKey) {
    StringBuilder localStringBuffer = new StringBuilder();
    localStringBuffer.append(appSecret);//APPSECRET
    localStringBuffer.append("&");
    localStringBuffer.append(appKey);
    localStringBuffer.append("&");
    localStringBuffer.append(body);
    localStringBuffer.append("&");
    localStringBuffer.append(timestamp);
    return MD5Util.sign(localStringBuffer.toString(), "UTF-8");
  }
}
