package com.shands.mod.main.controller.crm;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @desc 活动预约分布式锁
*/
import com.shands.mod.main.service.sys.ISequenceService;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import com.shands.mod.main.vo.SequenceType;
import com.shands.mod.vo.ResultVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/5/28
 */
@RestController
@RequestMapping("sequence")
@Deprecated
public class SequenceController {
  private final ISequenceService iSequenceService;

  public SequenceController(ISequenceService iSequenceService) {
    this.iSequenceService = iSequenceService;
  }

  @GetMapping("generate")
    @ResultLog(name = "SequenceController.generateSequence", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<String> generateSequence(SequenceType type, Integer length) {
    return ResultVO.success(iSequenceService.generateSequence(type, length));
  }
}
