package com.shands.mod.main.service.statistics;

import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;

import java.util.List;

/**
 * @Description: 酒店法宝统计服务接口
 * @Author: mazhiyong
 */
public interface VoucherStatisticService {
    /**
     * 查询酒店法宝相关数据
     * @param deptDataReq 请求参数
     * @return 酒店法宝相关数据
     */
    List<DeptDataVo> queryVoucherDeptData(DeptDataReq deptDataReq);
}
