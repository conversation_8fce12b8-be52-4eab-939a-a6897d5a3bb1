package com.shands.mod.main.controller.roomclean;

import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.req.clean.QueryCleanRoomNumReq;
import com.shands.mod.dao.model.req.clean.QueryCleanRoomUserReq;
import com.shands.mod.dao.model.res.clean.CleanRoomUserRes;
import com.shands.mod.main.service.clean.ICleanRoomServie;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

/**
 * 做房服务-对外
 * <AUTHOR>
 * @date 2023/8/23
 **/
@RestController
@RequestMapping(value = "/cleanRoomForOut")
public class CleanRoomForOutController extends BaseController {

  @Resource
  private ICleanRoomServie cleanRoomServie;


  /**
   * 查询清扫人员列表
   *
   * @param cleanRoomUserReq 清扫人员请求参数
   * @return 响应
   */
  @PostMapping("/queryCleanRoomUser")
  @ApiModelProperty(value = "查询清扫人员列表")
  public ResultVO<List<CleanRoomUserRes>> queryCleanRoomUser(
      @RequestBody QueryCleanRoomUserReq cleanRoomUserReq) {
    List<CleanRoomUserRes> cleanRoomUserResList = cleanRoomServie.queryCleanRoomUser(
        cleanRoomUserReq);
    return ResultVO.success(cleanRoomUserResList);
  }

  /**
   * 查询清扫房间列表
   *
   * @param cleanRoomNumReq 清扫房间请求参数
   * @return 响应
   */
  @PostMapping("/queryCleanRoomNum")
  @ApiModelProperty(value = "查询清扫房间列表")
  public ResultVO<List<String>> queryCleanRoomNum(
      @RequestBody QueryCleanRoomNumReq cleanRoomNumReq) {
    List<String> cleanRoomNumResList = cleanRoomServie.queryCleanRoomNum(cleanRoomNumReq);
    return ResultVO.success(cleanRoomNumResList);
  }

  @Override
  public boolean isPublic() {
    return true;
  }
}
