package com.shands.mod.main.controller.statistics;

import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.statistics.req.BusinessDataDetailReq;
import com.shands.mod.dao.model.statistics.req.CommonStatisticReq;
import com.shands.mod.dao.model.statistics.vo.BaseStatisticsDataVo;
import com.shands.mod.dao.model.statistics.vo.BetterWoodMemberDataVo;
import com.shands.mod.dao.model.statistics.vo.BusinessDataDetailVo;
import com.shands.mod.dao.model.statistics.vo.RoomNightDetailVo;
import com.shands.mod.dao.model.statistics.vo.SellCardRankingVo;
import com.shands.mod.main.service.statistics.StatisticsDataService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 统计模块相关接口
 * @Author: mazhiyong
 */
@RestController
@RequestMapping("/statistics")
@Api(value = "统计模块", tags = "统计模块")
@Slf4j
public class StatisticsDataController {

    @Autowired
    private StatisticsDataService statisticsDataService;

    /**
     * 经营数据概况
     *
     * @param req 请求参数
     * @return 经营数据概况
     */
    @ApiOperation(value = "统计模块-经营情况-经营数据")
    @PostMapping("/briefBusinessData")
    @ApiResponse(code = 0, message = "成功", response = BaseStatisticsDataVo.class)
    public ResultVO<BaseStatisticsDataVo> briefBusinessData(@Valid @RequestBody CommonStatisticReq req) {
        BaseStatisticsDataVo result = statisticsDataService.getBriefBusinessData(req);
        return ResultVO.success(result);
    }


    /**
     * 经营数据详情指标获取
     *
     * @return 经营数据详情指标
     */
    @ApiOperation(value = "统计模块-经营数据详情指标获取")
    @GetMapping("/businessDataIndicator")
    @ApiResponse(code = 0, message = "成功", response = BaseStatisticsDataVo.class)
    public ResultVO<List<HomeDataDetailsVo>> getBusinessDataIndicator() {
        List<HomeDataDetailsVo> result = statisticsDataService.getBusinessDataIndicator();
        return ResultVO.success(result);
    }

    /**
     * 经营数据详情
     *
     * @param req 请求参数
     * @return 经营数据概况
     */
    @ApiOperation(value = "统计模块-经营情况-数据详情")
    @PostMapping("/businessDataDetail")
    @ApiResponse(code = 0, message = "成功", response = BusinessDataDetailVo.class)
    public ResultVO<BusinessDataDetailVo> getBusinessDataDetail(@Valid @RequestBody BusinessDataDetailReq req) {
        BusinessDataDetailVo result = statisticsDataService.getBusinessDataDetail(req);
        return ResultVO.success(result);
    }


    /**
     * 已售间夜分布
     *
     * @param req 请求参数
     * @return 已售间夜分布
     */
    @ApiOperation(value = "统计模块-经营情况-间夜分布")
    @PostMapping("/roomNightDetail")
    @ApiResponse(code = 0, message = "成功", response = RoomNightDetailVo.class)
    public ResultVO<RoomNightDetailVo> getRoomNightDetailData(@Valid @RequestBody CommonStatisticReq req) {
        RoomNightDetailVo result = statisticsDataService.getRoomNightDetailData(req);
        return ResultVO.success(result);
    }

    /**
     * 会员数据
     *
     * @param req 请求参数
     * @return 会员数据
     */
    @ApiOperation(value = "统计模块-会员数据")
    @PostMapping("/betterWoodMemberData")
    @ApiResponse(code = 0, message = "成功", response = BetterWoodMemberDataVo.class)
    public ResultVO<BetterWoodMemberDataVo> getBetterWoodMemberData(@Valid @RequestBody CommonStatisticReq req) {
        BetterWoodMemberDataVo result = statisticsDataService.getBetterWoodMemberData(req);
        return ResultVO.success(result);
    }

    /**
     * 门店售卡奖金排行榜
     *
     * @param req 请求参数
     * @return 门店售卡奖金排行榜数据
     */
    @ApiOperation(value = "统计模块-会员数据-门店售卡奖金排行榜")
    @PostMapping("/sellCardRanking")
    @ApiResponse(code = 0, message = "成功", response = SellCardRankingVo.class)
    public ResultVO<SellCardRankingVo> getSellCardRanking(@Valid @RequestBody CommonStatisticReq req) {
        SellCardRankingVo result = statisticsDataService.getSellCardRanking(req);
        return ResultVO.success(result);
    }
}
