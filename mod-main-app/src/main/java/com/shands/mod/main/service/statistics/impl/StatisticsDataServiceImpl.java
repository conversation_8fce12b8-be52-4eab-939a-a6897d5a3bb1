package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.datarevision.AdsAppTradeRevenueDMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.DateTypeEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto;
import com.shands.mod.dao.model.statistics.dto.EmployeeRankingItem;
import com.shands.mod.dao.model.statistics.dto.HotelRankingItem;
import com.shands.mod.dao.model.statistics.req.BusinessDataDetailReq;
import com.shands.mod.dao.model.statistics.req.CommonStatisticReq;
import com.shands.mod.dao.model.statistics.vo.*;
import com.shands.mod.dao.model.statistics.vo.SellCardRankingVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.enums.TrendTypeEnum;
import com.shands.mod.main.service.statistics.StatisticsDataService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 经营数据概况服务实现
 * @Author: mazhiyong
 */
@Service
@Slf4j
public class StatisticsDataServiceImpl implements StatisticsDataService {

    @Autowired
    private AdsAppTradeRevenueDMapper adsAppTradeRevenueDMapper;

    @Autowired
    private ModHotelInfoDao hotelInfoDao;

    @Autowired
    private ModUserCommonService modUserCommonService;

    @Autowired
    private ModNewDataBoardMapper modNewDataBoardMapper;

    private static final List<String> ROOM_NIGHT_DATA_LIST = Lists.newArrayList(
            DataBoardModuleEnum.SOLD_OUT_ROOM_NIGHT_DATA.name(),
            DataBoardModuleEnum.BETTERWODD_ROOM_NIGHT_DATA.name(),
            DataBoardModuleEnum.COOPERATION_CHANNEL_ROOM_NIGHT_DATA.name(),
            DataBoardModuleEnum.OFFLINE_ROOM_NIGHT_DATA.name()
    );

    /**
     * 获取经营数据概况
     *
     * @param req 请求参数
     * @return 经营数据概况
     */
    @Override
    public BaseStatisticsDataVo getBriefBusinessData(CommonStatisticReq req) {
        BaseStatisticsDataVo res = new BaseStatisticsDataVo();

        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        final String businessDataCode = DataBoardModuleEnum.BUSINESS_DATA.name();
        ModuleMenuVo vo = getModuleMenu(businessDataCode);
        // 3. 获取指标列表
        List<HomeDataDetailsVo> dataList = getIndicatorList(businessDataCode);
        if (Objects.isNull(vo) || CollUtil.isEmpty(dataList)) {
            log.error("用户权限不足, userId:{}", userId);
            res.setShowFlag(false);
            return res;
        }

        // 4. 查询当前数据
        AdsAppTradeRevenueDDto currentData = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), hotelCode);


        // 5. 默认对比去年数据
        DateTime lastStartTime = DateUtil.offset(req.getCurrentStartTime(), DateField.YEAR, -1);
        DateTime lastEndTime = DateUtil.offset(req.getCurrentEndTime(), DateField.YEAR, -1);

        AdsAppTradeRevenueDDto lastData = adsAppTradeRevenueDMapper.selectRangeData(
                lastStartTime, lastEndTime, hotelCode);


        for (HomeDataDetailsVo detailsVo : dataList) {
            // 增降幅
            this.setBriefBusinessDataTrendRate(currentData, lastData, detailsVo);
            // 赋值操作
            this.fillBriefBusinessDataValue(currentData, detailsVo);
        }
        // 构建返回结果
        res.setDataList(dataList);
        res.setTitle(vo.getModuleName());
        res.setShowFlag(true);
        res.setTitleDesc(vo.getDescName());

        return res;
    }

    @Override
    public List<HomeDataDetailsVo> getBusinessDataIndicator() {

        return getIndicatorList(DataBoardModuleEnum.BUSINESS_DATA.name());
    }

    private ModuleMenuVo getModuleMenu(String moduleCode) {
        // 获取当前用户id
        Integer userId = ThreadLocalHelper.getUser().getId();

        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);
        ModuleMenuVo vo = dataBoards.stream()
                .filter(v -> moduleCode.equals(v.getModuleCode()))
                .findFirst().orElse(null);
        return vo;
    }

    public List<HomeDataDetailsVo> getIndicatorList(String moduleCode) {
        // 1. 获取当前用户id
        Integer userId = ThreadLocalHelper.getUser().getId();

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles,userId);
        ModuleMenuVo vo = getModuleMenu(moduleCode);
        if (Objects.isNull(vo)) {
            return Lists.newArrayList();
        }

        // 5. 获取指标列表
        List<ModuleMenuVo> indicatorList = dataBoards.stream()
                .filter(v -> v.getPId().equals(vo.getId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(vo.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();

            dataList.add(detailsVo);
        }

        return dataList;
    }


    /**
     * 获取经营数据详情
     *
     * @param req 请求参数
     * @return 经营数据详情
     */
    @Override
    public BusinessDataDetailVo getBusinessDataDetail(BusinessDataDetailReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        ModuleMenuVo vo = getModuleMenu(req.getDataType());
        if (Objects.isNull(vo)) {
            throw new ServiceException("用户权限不足,请联系管理员！");
        }

        DateTime lastStartTime = DateUtil.offset(req.getCurrentStartTime(), DateField.YEAR, -1);
        DateTime lastEndTime = DateUtil.offset(req.getCurrentEndTime(), DateField.YEAR, -1);

        BusinessDataDetailVo res = new BusinessDataDetailVo();
        res.setShowFlag(true);
        res.setTitle(vo.getModuleName());
        res.setTitleDesc(vo.getDescName());
        res.setUnit(vo.getUnit());

        List<BusinessDataDetail> dateDetailList = new ArrayList<>();

        if (StrUtil.equals(req.getTimeType(), DateTypeEnum.DAY.name()) || StrUtil.equals(req.getTimeType(), DateTypeEnum.MONTH.name())) {
            // 查询每日数据
            List<AdsAppTradeRevenueDDto> currentDataList = adsAppTradeRevenueDMapper.selectRangeDataList(req.getCurrentStartTime(), req.getCurrentEndTime(), hotelCode);
            List<AdsAppTradeRevenueDDto> lastDataList = adsAppTradeRevenueDMapper.selectRangeDataList(lastStartTime, lastEndTime, hotelCode);
            // 转化成map
            Map<Date, AdsAppTradeRevenueDDto> currentDataMap = currentDataList.stream()
                    .collect(Collectors.toMap(AdsAppTradeRevenueDDto::getBizDate, Function.identity(), (k1, k2) -> k1));
            Map<Date, AdsAppTradeRevenueDDto> lastDataMap = lastDataList.stream()
                    .collect(Collectors.toMap(AdsAppTradeRevenueDDto::getBizDate, Function.identity(), (k1, k2) -> k1));

            // 日期递增
            Date currentMoveDate = new Date(req.getCurrentStartTime().getTime());
            while (DateUtil.compare(currentMoveDate, req.getCurrentEndTime()) <= 0) {

                Date lastMoveDate = DateUtil.offset(currentMoveDate, DateField.YEAR, -1);

                BusinessDataDetail dataDetail = new BusinessDataDetail();
                dataDetail.setCurrentDate(DateUtil.format(currentMoveDate, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE4)));
                dataDetail.setLastDate(DateUtil.format(lastMoveDate, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE4)));
                // 设置本期值
                AdsAppTradeRevenueDDto currentAdsAppTradeRevenueDDto = currentDataMap.get(currentMoveDate);
                dataDetail.setCurrentValue(getBusinessDataDetailValue(currentAdsAppTradeRevenueDDto, req.getDataType(), res.getUnit()));
                // 设置往期值
                AdsAppTradeRevenueDDto lastAdsAppTradeRevenueDDto = lastDataMap.get(lastMoveDate);
                dataDetail.setLastValue(getBusinessDataDetailValue(lastAdsAppTradeRevenueDDto, req.getDataType(), res.getUnit()));

                // 设置增降幅
                setBusinessDataDetailTrendRate(currentAdsAppTradeRevenueDDto, lastAdsAppTradeRevenueDDto, dataDetail, req.getDataType());

                currentMoveDate = DateUtil.offsetDay(currentMoveDate, 1);
                dateDetailList.add(dataDetail);
            }

        } else {
            // 月度数据，单位改为元
            if (StrUtil.equals("元", vo.getUnit())) {
                res.setUnit("万");
            }
            // 月份递增
            Date currentMoveMonth = DateUtil.beginOfMonth(req.getCurrentStartTime());
            while (DateUtil.compare(currentMoveMonth, DateUtil.beginOfMonth(req.getCurrentEndTime())) <= 0) {

                Date lastMoveMonth = DateUtil.offset(currentMoveMonth, DateField.YEAR, -1);

                AdsAppTradeRevenueDDto currentMonthData = adsAppTradeRevenueDMapper.selectRangeData(currentMoveMonth, DateUtil.endOfMonth(currentMoveMonth), hotelCode);
                AdsAppTradeRevenueDDto lastMonthData = adsAppTradeRevenueDMapper.selectRangeData(lastMoveMonth, DateUtil.endOfMonth(lastMoveMonth), hotelCode);

                BusinessDataDetail detail = new BusinessDataDetail();
                detail.setCurrentDate(DateUtil.format(currentMoveMonth, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE5)));
                detail.setLastDate(DateUtil.format(lastMoveMonth, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE5)));
                // 设置本期值
                detail.setCurrentValue(getBusinessDataDetailValue(currentMonthData, req.getDataType(), res.getUnit()));
                // 设置往期值
                detail.setLastValue(getBusinessDataDetailValue(lastMonthData, req.getDataType(), res.getUnit()));

                // 设置增降幅
                setBusinessDataDetailTrendRate(currentMonthData, lastMonthData, detail, req.getDataType());

                currentMoveMonth = DateUtil.offsetMonth(currentMoveMonth, 1);
                dateDetailList.add(detail);
            }
        }
        res.setDateDetail(dateDetailList);

        return res;

    }


    @Override
    public RoomNightDetailVo getRoomNightDetailData(CommonStatisticReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
        req.setHotelCode(hotelCode);

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        RoomNightDetailVo res = new RoomNightDetailVo();

        // 已售间夜
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.SOLD_OUT_ROOM_NIGHT_DATA.name()) ) {
            res.getSoldOutRoomNight().setShowFlag(false);
        } else {
            setSoldOutRoomNightData(req, res, moduleMenuVos);
        }

        // 百达屋间夜
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.BETTERWODD_ROOM_NIGHT_DATA.name()) ) {
            res.getBetterWoodRoomNight().setShowFlag(false);
        } else {
            setBetterWoodRoomNightData(req, res, moduleMenuVos);
        }

        // 集团合作渠道间夜
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.COOPERATION_CHANNEL_ROOM_NIGHT_DATA.name()) ) {
            res.getCooperationChannelRoomNight().setShowFlag(false);
        } else {
            setCooperationChannelRoomNightData(req, res, moduleMenuVos);
        }

        // 线下间夜
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.OFFLINE_ROOM_NIGHT_DATA.name()) ) {
            res.getOfflineRoomNight().setShowFlag(false);
        } else {
            setOfflineRoomNightData(req, res, moduleMenuVos);
        }

        return res;
    }


    /**
     * 设置已售间夜数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setSoldOutRoomNightData(CommonStatisticReq req, RoomNightDetailVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo soldOutRoomNight = res.getSoldOutRoomNight();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.SOLD_OUT_ROOM_NIGHT_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        soldOutRoomNight.setShowFlag(true);
        soldOutRoomNight.setTitle(bigModuleMenu.getModuleName());
        soldOutRoomNight.setTitleDesc(bigModuleMenu.getDescName());
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        soldOutRoomNight.setDataList(dataList);
    }

    /**
     * 设置百达屋间夜数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setBetterWoodRoomNightData(CommonStatisticReq req, RoomNightDetailVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo betterWoodRoomNight = res.getBetterWoodRoomNight();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.BETTERWODD_ROOM_NIGHT_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        betterWoodRoomNight.setShowFlag(true);
        betterWoodRoomNight.setTitle(bigModuleMenu.getModuleName());
        betterWoodRoomNight.setTitleDesc(bigModuleMenu.getDescName());
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        betterWoodRoomNight.setDataList(dataList);
    }

    /**
     * 设置集团合作渠道间夜数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setCooperationChannelRoomNightData(CommonStatisticReq req, RoomNightDetailVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo cooperationChannelRoomNight = res.getCooperationChannelRoomNight();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.COOPERATION_CHANNEL_ROOM_NIGHT_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        cooperationChannelRoomNight.setShowFlag(true);
        cooperationChannelRoomNight.setTitle(bigModuleMenu.getModuleName());
        cooperationChannelRoomNight.setTitleDesc(bigModuleMenu.getDescName());
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        cooperationChannelRoomNight.setDataList(dataList);
    }

    /**
     * 设置线下间夜数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setOfflineRoomNightData(CommonStatisticReq req, RoomNightDetailVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo offlineRoomNight = res.getOfflineRoomNight();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.OFFLINE_ROOM_NIGHT_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        offlineRoomNight.setShowFlag(true);
        offlineRoomNight.setTitle(bigModuleMenu.getModuleName());
        offlineRoomNight.setTitleDesc(bigModuleMenu.getDescName());
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        offlineRoomNight.setDataList(dataList);
    }

    /**
     * 获取会员数据详情
     *
     * @param req 请求参数
     * @return 会员数据详情
     */
    @Override
    public BetterWoodMemberDataVo getBetterWoodMemberData(CommonStatisticReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
        req.setHotelCode(hotelCode);

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        BetterWoodMemberDataVo res = new BetterWoodMemberDataVo();

        // 全网APP消费会员数据
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.APP_CONSUME_MEMBER_DATA.name())) {
            res.getAppConsumeMemberData().setShowFlag(false);
        } else {
            setAppConsumeMemberData(req, res, moduleMenuVos);
        }

        // 会员业绩归属数据
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.MEMBER_PERFORMANCE_BELONG_DATA.name())) {
            res.getMemberPerformanceBelongData().setShowFlag(false);
        } else {
            setMemberPerformanceBelongData(req, res, moduleMenuVos);
        }

        // 门店法宝数据
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.HOTEL_VOUCHER_DATA.name())) {
            res.getHotelVoucherData().setShowFlag(false);
        } else {
            setHotelVoucherData(req, res, moduleMenuVos);
        }

        // 百达卡销售数据
        if (!CollUtil.contains(moduleMenuVos, DataBoardModuleEnum.BETTERWOOD_CARD_SELL_DATA.name())) {
            res.getBetterWoodCardSellData().setShowFlag(false);
        } else {
            setBetterWoodCardSellData(req, res, moduleMenuVos);
        }

        return res;
    }

    /**
     * 设置APP消费会员数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setAppConsumeMemberData(CommonStatisticReq req, BetterWoodMemberDataVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo appConsumeMemberData = res.getAppConsumeMemberData();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.APP_CONSUME_MEMBER_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        appConsumeMemberData.setShowFlag(true);
        appConsumeMemberData.setTitle(bigModuleMenu.getModuleName());
        appConsumeMemberData.setTitleDesc(bigModuleMenu.getDescName());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        appConsumeMemberData.setDataList(dataList);
    }

    /**
     * 设置会员业绩归属数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setMemberPerformanceBelongData(CommonStatisticReq req, BetterWoodMemberDataVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo memberPerformanceBelongData = res.getMemberPerformanceBelongData();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.MEMBER_PERFORMANCE_BELONG_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        memberPerformanceBelongData.setShowFlag(true);
        memberPerformanceBelongData.setTitle(bigModuleMenu.getModuleName());
        memberPerformanceBelongData.setTitleDesc(bigModuleMenu.getDescName());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        memberPerformanceBelongData.setDataList(dataList);
    }

    /**
     * 设置门店法宝数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setHotelVoucherData(CommonStatisticReq req, BetterWoodMemberDataVo res, List<ModuleMenuVo> moduleMenuVos) {
        BaseStatisticsDataVo hotelVoucherData = res.getHotelVoucherData();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.HOTEL_VOUCHER_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        hotelVoucherData.setShowFlag(true);
        hotelVoucherData.setTitle(bigModuleMenu.getModuleName());
        hotelVoucherData.setTitleDesc(bigModuleMenu.getDescName());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        hotelVoucherData.setDataList(dataList);
    }

    /**
     * 设置百达卡销售数据
     * @param req 请求参数
     * @param res 返回结果
     * @param moduleMenuVos 模块菜单列表
     */
    private void setBetterWoodCardSellData(CommonStatisticReq req, BetterWoodMemberDataVo res, List<ModuleMenuVo> moduleMenuVos) {
        BetterWoodCardSellDataVo betterWoodCardSellData = res.getBetterWoodCardSellData();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> DataBoardModuleEnum.BETTERWOOD_CARD_SELL_DATA.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());
        betterWoodCardSellData.setShowFlag(true);
        betterWoodCardSellData.setTitle(bigModuleMenu.getModuleName());
        betterWoodCardSellData.setTitleDesc(bigModuleMenu.getDescName());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        // 查询数据
        AdsAppTradeRevenueDDto data = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillBriefBusinessDataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        betterWoodCardSellData.setDataList(dataList);

        // 设置百达卡销售占比数据
        List<BetterWoodCardDetailVo> cardDetailList = new ArrayList<>();
        // 这里应该是从数据库查询，但为了演示，我们模拟一些数据
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("启航卡")
                .cardNum(15)
                .cardBonus(45300L)
                .build());
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("漫步卡")
                .cardNum(12)
                .cardBonus(42300L)
                .build());
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("开拓卡")
                .cardNum(8)
                .cardBonus(25310L)
                .build());

        // 设置百达卡销售占比数据
        betterWoodCardSellData.setCardDetailList(cardDetailList);
    }

    /**
     * 获取门店售卡奖金排行榜
     *
     * @param req 请求参数
     * @return 门店售卡奖金排行榜数据
     */
    @Override
    public SellCardRankingVo getSellCardRanking(CommonStatisticReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
        req.setHotelCode(hotelCode);

        SellCardRankingVo result = new SellCardRankingVo();

        // 2. 检查用户权限
        final String cardRankDataCode = DataBoardModuleEnum.BETTERWOOD_CARD_RANK_DATA.name();
        ModuleMenuVo vo = getModuleMenu(cardRankDataCode);
        if (Objects.isNull(vo)) {
            log.error("用户权限不足, userId:{}", userId);
            result.setShowFlag(false);
            return result;
        }

        // 3. 构建返回结果
        result.setTitle(vo.getModuleName());
        result.setShowFlag(true);

        // 4. 查询数据
        // 门店排行榜
        List<HotelRankingItem> hotelRanking = new ArrayList<>();
        hotelRanking.add(HotelRankingItem.builder()
                .hotelName("杭州开元名都大酒店")
                .cardNum(56L)
                .cardBonus(567900L)
                .build());

        result.setHotelRanking(hotelRanking);

        // 员工排行榜
        List<EmployeeRankingItem> employeeRanking = new ArrayList<>();
        employeeRanking.add(EmployeeRankingItem.builder()
                .hotelName("杭州开元名都大酒店")
                .employeeName("张三")
                .cardNum(56L)
                .cardBonus(567900L)
                .build());

        result.setEmployeeRanking(employeeRanking);

        return result;
    }



    private void setBusinessDataDetailTrendRate(AdsAppTradeRevenueDDto currentData, AdsAppTradeRevenueDDto lastData,
                                                BusinessDataDetail detail, String code) {
        if (Objects.isNull(lastData) || Objects.isNull(currentData)) {
            detail.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detail.setTrendRate("-");
            return;
        }

        BigDecimal currentValue = (BigDecimal) currentData.getValueByFieldName(code);
        BigDecimal lastValue = (BigDecimal) lastData.getValueByFieldName(code);

        if (Objects.isNull(currentValue) || Objects.isNull(lastValue)) {
            detail.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detail.setTrendRate("-");
            return;
        }

        if (currentValue.compareTo(lastValue) == 0 || BigDecimal.ZERO.compareTo(currentValue) == 0 || BigDecimal.ZERO.compareTo(lastValue) == 0) {
            detail.setTrendType(TrendTypeEnum.EQUAL.getCode());
            detail.setTrendRate("0");
        } else if (currentValue.compareTo(lastValue) > 0) {
            detail.setTrendType(TrendTypeEnum.UP.getCode());
            detail.setTrendRate(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue).toString() + "%");
        } else {
            detail.setTrendType(TrendTypeEnum.DOWN.getCode());
            detail.setTrendRate(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue).toString() + "%");
        }

    }



    private String getBusinessDataDetailValue(AdsAppTradeRevenueDDto data, String code, String unit) {
        if (Objects.isNull(data)) {
            return null;
        }
        Object value = data.getValueByFieldName(code);
        String valueStr = "";
        if (Objects.nonNull(value)) {
            if (StrUtil.equals("万", unit)) {
                BigDecimal bigDecimalValue = (BigDecimal) value;
                BigDecimal TEN_THOUSAND = new BigDecimal("10000");

                valueStr = bigDecimalValue.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP).toString();
            } else {
                valueStr = String.valueOf(value);
            }
        }
        return valueStr;
    }

    private void fillBriefBusinessDataValue(AdsAppTradeRevenueDDto data, HomeDataDetailsVo detailsVo) {
        if (data == null) {
            return;
        }

        String code = detailsVo.getCode();
        Object value = data.getValueByFieldName(code);
        if (Objects.nonNull(value)) {
            detailsVo.setValue(String.valueOf(value));
            if (value instanceof BigDecimal) {
                BigDecimal bigDecimalValue = (BigDecimal) value;
                BigDecimal TEN_THOUSAND = new BigDecimal("10000");

                if (TEN_THOUSAND.compareTo(bigDecimalValue) <= 0) {
                    detailsVo.setValue(bigDecimalValue.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP).toString());
                    detailsVo.setUnit("万");
                }
            }
        }
    }

    /**
     * 设置趋势率
     * trendType Integer 增或降（-1 ： 降 0：平 1：增 2：无法比较 ）
     * trendRate String  增降幅(-4.01%)
     * 1. 若往期无数据，增降幅显示“-”并置灰。接口返回currentValue或lastValue：null，trendType：2 trendRate：-
     * 2. 若本期/往期值为零，增降幅显示“0”。接口返回currentValue或lastValue：0，trendType：0 trendRate：0
     * @param currentData 当前数据
     * @param lastData    上期数据
     * @param detailsVo   指标详情
     */
    private void setBriefBusinessDataTrendRate(AdsAppTradeRevenueDDto currentData, AdsAppTradeRevenueDDto lastData, HomeDataDetailsVo detailsVo) {
        if (Objects.isNull(currentData) || Objects.isNull(lastData)) {
            detailsVo.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detailsVo.setTrendRate("-");
            return;
        }

        BigDecimal currentValue =(BigDecimal) currentData.getValueByFieldName(detailsVo.getCode());
        BigDecimal lastValue =(BigDecimal) lastData.getValueByFieldName(detailsVo.getCode());

        if (Objects.isNull(currentValue) || Objects.isNull(lastValue)) {
            detailsVo.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detailsVo.setTrendRate("-");
            return;
        }
        if (currentValue.compareTo(lastValue) == 0 || BigDecimal.ZERO.compareTo(currentValue) == 0 || BigDecimal.ZERO.compareTo(lastValue) == 0) {
            detailsVo.setTrendType(TrendTypeEnum.EQUAL.getCode());
            detailsVo.setTrendRate("0");
        } else if (currentValue.compareTo(lastValue) > 0) {
            detailsVo.setTrendType(TrendTypeEnum.UP.getCode());
            detailsVo.setTrendRate(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue).toString() + "%");
        } else {
            detailsVo.setTrendType(TrendTypeEnum.DOWN.getCode());
            detailsVo.setTrendRate(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue).toString() + "%");
        }


    }
}
