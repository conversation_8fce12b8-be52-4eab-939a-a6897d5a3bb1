package com.shands.mod.main.service.message;

import com.shands.mod.dao.model.v0701.pojo.ModMessageLog;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19
 * @desc 消息中心service类
*/

public interface ShandsMessageService {

  /**
   * Direct队列 友盟消息推送
   * @param topic 主题
   * @param expendNo 第三方订单号
   * @param userId 用户ID
   * @param text 消息内容
   * @param title 消息标题
   * @param messageCode 消息code
   * @param serviceType 服务类型
   * @param companyId 酒店编码
   * @param groupId 集团编码
   * @param soundFile 提示音文件
   * @param noticeCode 通知类型编码
   */
  void sendDirectYmMsg(String topic,String expendNo,Integer userId,String text,String title,String messageCode,String serviceType,Integer companyId,Integer groupId,String soundFile,String noticeCode);

  /**
   * Direct队列 友盟消息推送 返回消息UUID
   * @param exchange 交换机名称
   * @param routingkey 路由键
   * @param expendNo 第三方订单号
   * @param userId 用户ID
   * @param text 消息内容
   * @param title 消息标题
   * @param messageCode 消息code
   * @param serviceType 服务类型
   * @param companyId 酒店编码
   * @param groupId 集团编码
   * @param soundFile 提示音文件
   * @return 消息UUID
   */
  String sendDirectYmMsgReturnUUID(String exchange,String routingkey,String expendNo,Integer userId,String text,String title,String messageCode,String serviceType,Integer companyId,Integer groupId,String soundFile);

  /**
   * Delay队列 友盟消息推送 返回消息UUID
   * @param topic 主题
   * @param delay 时长（单位：ms）
   * @param expendNo 第三方订单号
   * @param userId 用户ID
   * @param text 消息内容
   * @param title 消息标题
   * @param messageCode 消息code
   * @param serviceType 服务类型
   * @param companyId 酒店编码
   * @param groupId 集团编码
   * @param soundFile 提示音文件
   * @return 消息UUID
   */
  String sendDelayYmMsgReturnUUID(String topic,Integer delay,String expendNo,Integer userId,String text,String title,String messageCode,String serviceType,Integer companyId,Integer groupId,String soundFile);

  /**
   * 消息中心
   * @return ModMessageLog
   */
  List<ModMessageLog> messageForU();

  /**
   * 查询个人未读消息
   *
   * @return {@link List}<{@link ModMessageLog}>
   */
  List<ModMessageLog> getUnReadMessage();

  /**
   * 获取当前用户的账单通知消息
   *
   * @return {@link List}<{@link ModMessageLog}>
   */
  List<ModMessageLog> getBillNotifications();

  ModMessageLog selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(ModMessageLog record);

  /**
   *给pc端发消息
   * @param companyId
   * workOrderId 工单id
   * sendOne true(单独一个人发消息 如果是 需要传入userId) false(群发)
   */
  void sendAudio(Integer companyId,Integer workOrderId,boolean sendOne,Integer userId);

  int oneKeyRead(Integer readState,Integer userId);

  /**
   * 保存消息发送记录
   *
   * @param typeEnum       消息发送类型
   * @param title 消息标题
   * @param messageContent 消息内容
   * @param extendNo       第三方关联单号
   * @param extendType       第三方关联类型
   * @param rejtTypeEnum   接受人类型
   * @param rejtId         接受人ID
   * @param groupId        集团编码
   * @param companyId      酒店编码
   * @param remark         备注信息
   */
  int saveMessageLog(MessageTypeEnum typeEnum, String title, String messageContent, String extendType, String extendNo,
      MessageTypeEnum rejtTypeEnum, String rejtId,
      int groupId, int companyId, String remark);

  /**
   * 修复数据
   * @return
   */
  int coverData();
}
