package com.shands.mod.main.service.token.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.token.PmsTokenBindHotelReq;
import com.shands.mod.dao.model.token.PmsTokenReq;
import com.shands.mod.dao.model.token.PmsTokenRsp;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.service.app.TbBindingPmsService;
import com.shands.mod.main.service.token.TokenService;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.util.JwtUtils;
import com.shands.mod.vo.ResultCode;
import com.shands.mod.vo.UserInfoVO;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/10
 * @desc 订单创建公共service实现类
 */
@Slf4j
@Component
public class TokenServiceImpl implements TokenService {

  @Value("${app.token.secret:shands-mod3-secret}")
  private String secret;

  @Autowired
  private ModUserCommonService modUserCommonService;
  @Autowired
  private TbBindingPmsService tbBindingPmsService;

  @Override
  public PmsTokenRsp getPmsToken(PmsTokenReq pmsTokenReq) {
    String token = pmsTokenReq.getToken();
    if (StringUtils.isEmpty(token) || JwtUtils.isTokenExpired(token, secret)) {
      throw new ServiceException(ResultCode.UNAUTH);
    }
    //查询用户信息
    UserInfoVO user = new UserInfoVO();
    try {
      user = modUserCommonService.getUserByTokenToPc(token);
      BaseThreadLocalHelper.setROLES(user.getRoles());
    } catch (Exception e) {
      ModUserLoginVo modUserLoginVo = modUserCommonService.getUserByToken(token);
      if (modUserLoginVo != null) {
        user.setId(modUserLoginVo.getId());
        user.setMobile(modUserLoginVo.getMobile());
        user.setUcId(modUserLoginVo.getUcId());
      }
    }
    String pmsToken = tbBindingPmsService.getPmsToken(user, pmsTokenReq.getExpiration(),pmsTokenReq.getHotelCode());
    if(StringUtils.isEmpty(pmsToken)) {
      throw new ServiceException(ResultCode.UNAUTH);
    }
    PmsTokenRsp pmsTokenRsp = new PmsTokenRsp();
    pmsTokenRsp.setPmsToken(pmsToken);
    return pmsTokenRsp;
  }

  @Override
  public Boolean bindPmsTokenHotel(PmsTokenBindHotelReq pmsTokenBindHotelReq) {
     return tbBindingPmsService.bindPmsTokenHotel(pmsTokenBindHotelReq.getPmsToken(),pmsTokenBindHotelReq.getHotelCode());
  }
}
