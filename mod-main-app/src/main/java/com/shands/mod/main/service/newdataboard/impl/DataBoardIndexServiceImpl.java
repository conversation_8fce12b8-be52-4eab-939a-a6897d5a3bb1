package com.shands.mod.main.service.newdataboard.impl;

import static com.shands.mod.dao.model.enums.DataIndexListEnum.*;
import static com.shands.mod.dao.model.enums.board.DataBoardModuleEnum.*;
import static com.shands.mod.util.DataBoardConstants.DEFAULT_HOTEL_CODE;
import static java.util.stream.Collectors.groupingBy;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.delonix.bi.dao.mapper.AdsTradeConsumerMemberMapper;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.board.ModUserDataDetailsMapper;
import com.shands.mod.dao.mapper.datarevision.DataBoardIndexMapper;
import com.shands.mod.dao.mapper.homerevision.DwsTradeRevenueIncDayDao;
import com.shands.mod.dao.model.board.ModUserDataDetails;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.DataIndexListEnum;
import com.shands.mod.dao.model.enums.DateTypeEnum;
import com.shands.mod.dao.model.enums.GroupTypeEnum;
import com.shands.mod.dao.model.enums.HotelTypeEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.enums.board.DataTypeEnum;
import com.shands.mod.dao.model.newDataBoard.bo.DataBoardIndexListBo;
import com.shands.mod.dao.model.newDataBoard.bo.DataBulletinBlocBoardBo;
import com.shands.mod.dao.model.newDataBoard.bo.DataBulletinBoardBo;
import com.shands.mod.dao.model.newDataBoard.bo.DataSummaryBo;
import com.shands.mod.dao.model.newDataBoard.bo.UpdateDataBoardIndexBo;
import com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo;
import com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo;
import com.shands.mod.dao.model.newDataBoard.vo.DataSummaryDetailedVo;
import com.shands.mod.dao.model.newDataBoard.vo.DataSummaryVo;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataInfoVo;
import com.shands.mod.dao.model.res.board.ModDataBoardListRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.newdataboard.DataBoardIndexService;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/10/27 16:40
 */
@Slf4j
@Service
public class DataBoardIndexServiceImpl implements DataBoardIndexService {

  @Resource
  private ModNewDataBoardMapper modNewDataBoardMapper;
  @Resource
  private ModUserDataDetailsMapper userDataDetailsMapper;
  @Resource
  private DwsTradeRevenueIncDayDao dwsTradeRevenueIncDayDao;
  @Resource
  private DataBoardIndexMapper dataBoardIndexMapper;
  @Resource
  private AdsTradeConsumerMemberMapper adsTradeConsumerMemberMapper;
  private final ModUserCommonService modUserCommonService;
  private final HotelInfoCommonService hotelInfoCommonService;

  @Autowired
  private RedisTemplate redisTemplate;

  public DataBoardIndexServiceImpl(
      ModUserCommonService modUserCommonService,
      HotelInfoCommonService hotelInfoCommonService) {
    this.modUserCommonService = modUserCommonService;
    this.hotelInfoCommonService = hotelInfoCommonService;
  }

  private String key;
  private int value = 0;
  @Value("${els.h5url:https://testx-m.kaiyuanhotels.com}")
  private String sxeUrl;

  public static final List<String> FILTER_BOARD_LIST = Arrays.asList(BUSINESS_CONDITION.name().toLowerCase(),
      BOOKING_TREND.name().toLowerCase(),
      FINAL_STATEMENT.name().toLowerCase(),
      ROOMS_SOLD_TODAY.name().toLowerCase(),
      DATA_BOARD_TODAY.name().toLowerCase(),
      DATA_BOARD_YESTERDAY.name().toLowerCase(),
      BUSINESS_DATA.name(),
      SOLD_OUT_ROOM_NIGHT_DATA.name(),
      BETTERWODD_ROOM_NIGHT_DATA.name(),
      COOPERATION_CHANNEL_ROOM_NIGHT_DATA.name(),
      OFFLINE_ROOM_NIGHT_DATA.name(),
      APP_CONSUME_MEMBER_DATA.name());
  @Override
  public Map<String,Object> moduleMenuList(DataBoardIndexListBo dataBoardIndexListBo) {
    Map<String,Object> map = new HashMap<>();
    Integer userId = ThreadLocalHelper.getUser().getId();
    try {

      List<String> userRoles = modUserCommonService.getUserRoles(userId);
      //所有指标
      List<ModuleMenuVo> moduleMenuDetailList = modNewDataBoardMapper.findModuleMenuDetailList(dataBoardIndexListBo.getType(),userRoles,userId);

      List<ModuleMenuVo> res = new ArrayList<>();
      Map<Integer, List<ModuleMenuVo>> listMap = moduleMenuDetailList.stream()
          .filter(x -> 3 != x.getPId() && 4 != x.getPId() && 3 != x.getId() && 4 != x.getId())
          // 20241031 设置指标过滤以下指标
          .filter(x-> !FILTER_BOARD_LIST.contains(x.getModuleCode()))
          .collect(groupingBy(ModuleMenuVo::getPId));
      if ("Y".equals(dataBoardIndexListBo.getDisplayFlag())){
        listMap = moduleMenuDetailList.stream().collect(groupingBy(ModuleMenuVo::getPId));
      }

      //得到父类
      List<ModuleMenuVo> pBoard = listMap.get(0).stream().sorted(Comparator.comparing(ModuleMenuVo::getSort)).collect(Collectors.toList());
      for (ModuleMenuVo moduleMenuVo : pBoard) {
        ModuleMenuVo vo = new ModuleMenuVo();
        vo.setId(moduleMenuVo.getId());
        vo.setPId(moduleMenuVo.getPId());
        vo.setDescName(moduleMenuVo.getDescName());
        vo.setModuleCode(moduleMenuVo.getModuleCode());
        vo.setModuleName(moduleMenuVo.getModuleName());
        vo.setUnit(moduleMenuVo.getUnit());
        vo.setChild(listMap.get(moduleMenuVo.getId()));
        res.add(vo);
      }
      // 个人9个指标
      List<ModuleMenuVo> personalDetailList = userDataDetailsMapper.findPersonalDetailList(dataBoardIndexListBo.getType(),userId);
      map.put("personalList",personalDetailList);
      map.put("detailList",res);
    } catch (Exception e) {
      log.error("moduleMenuList_查询指标异常 "+ userId, e);
    }

    return map;
  }

  @Override
  public boolean updateDataBoardIndex(UpdateDataBoardIndexBo updateDataBoardIndexBo) {
    Integer userId = ThreadLocalHelper.getUser().getId();
    //删除该类型下的所有个人指标
    List<ModUserDataDetails> list = new ArrayList<>();;
    try {
      AtomicInteger a = new AtomicInteger(1);
      userDataDetailsMapper.deleteByUserId(userId,updateDataBoardIndexBo.getType());
      for (Integer id : updateDataBoardIndexBo.getIds()) {
        ModUserDataDetails modUserDataDetails = ModUserDataDetails.builder()
            .boardId(id)
            .userId(userId)
            .sort(a.getAndIncrement())
            .type(updateDataBoardIndexBo.getType())
            .createTime(new Date())
            .updateTime(new Date())
            .build();
        list.add(modUserDataDetails);
      }
    } catch (Exception e) {
      log.error("updateDataBoardIndex_异常_ " + userId, e);
      return false;
    }
    return userDataDetailsMapper.insertBatch(list) > 0;
  }

  @Override
  public HomeDataInfoVo homeData(String type){
    Integer userId = ThreadLocalHelper.getUser().getId();
    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    // 先查默认六个  如果没有  手动加9个
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(type, userRoles,userId);
    List<String> filterBoardList = this.getFilterBoardList(FILTER_BOARD_LIST);
    List<ModuleMenuVo> collect = dataBoards.stream().filter(
            x -> 0 != x.getPId() && !"blocGrossRevenueBudget".equals(x.getModuleCode())
                && !"blocRoomRevenueBudget".equals(x.getModuleCode())
                && !"blocCateringRevenueBudget".equals(x.getModuleCode())
                && !"hotelGrossRevenueBudget".equals(x.getModuleCode())
                && !"hotelRoomRevenueBudget".equals(x.getModuleCode())
                && !"hotelCateringRevenueBudget".equals(x.getModuleCode()))
        // 20241031 设置指标过滤以下指标
        .filter(x-> !filterBoardList.contains(x.getModuleCode()))
        .collect(Collectors.toList());
    //默认9个指标
    this.setDefaultIndex(type,userId,collect);
    //查询数据
    return this.findHomeData(type, userId,collect);
  }

  private List<String> getFilterBoardList(List<String> codes) {
    Object obj = redisTemplate.opsForValue().get(BaseConstants.FILTER_BOARD_CODES_KEY);
    if (Objects.nonNull(obj) && obj instanceof List) {
      return (List<String>) obj;
    }
    List<String> codeList = this.filterByFirstLevels(codes);
    redisTemplate.opsForValue().set(BaseConstants.FILTER_BOARD_CODES_KEY, codeList);
    return codeList;
  }

  /**
   * 根据第一层数据权限code获取子权限
   * @param codes
   * @return
   */
  private List<String> filterByFirstLevels(List<String> codes){
    Set<Integer> filterIds = new HashSet<>();
    List<ModDataBoardListRes> boards = modNewDataBoardMapper.queryAll();
    // 一级
    List<ModDataBoardListRes> firstLevels = boards.stream()
        .filter(x -> codes.contains(x.getCode())).collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(firstLevels)) {
      addSubLevels(filterIds, firstLevels, boards);
    }

    return boards.stream().filter(x -> filterIds.contains(x.getId()))
        .map(ModDataBoardListRes::getCode).collect(
            Collectors.toList());
  }

  private void addSubLevels(Set<Integer> filterIds, List<ModDataBoardListRes> currentLevels, List<ModDataBoardListRes> boards) {
    if (currentLevels.isEmpty()) {
      return;
    }
    // 添加当前层级的ID
    currentLevels.forEach(board -> filterIds.add(board.getId()));
    // 获取下一层级的数据
    List<ModDataBoardListRes> nextLevels = boards.stream()
        .filter(x -> currentLevels.stream().anyMatch(board -> Objects.equals(board.getId(), x.getPId())))
        .collect(Collectors.toList());
    // 递归处理下一层级
    addSubLevels(filterIds, nextLevels, boards);
  }



  /**
   * 设置默认指标
   * @param type
   * @param userId
   */
  private void setDefaultIndex(String type,Integer userId,List<ModuleMenuVo> collect){
    // 先查默认9个  如果没有  手动加9个
    int indexValue = 6;
    List<ModUserDataDetails> modUserDataDetailsList = userDataDetailsMapper.queryByUserIdAndType(
        type, userId);
    if (modUserDataDetailsList.isEmpty()) {
      if (collect.isEmpty()) {
        return;
      }
      if (collect.size() > indexValue) {
        collect = collect.subList(0, indexValue);
      }
    } else {
      List<ModuleMenuVo> finalCollect = collect;
      if (modUserDataDetailsList.size() > finalCollect.size() || (
          modUserDataDetailsList.size() < indexValue && finalCollect.size() > indexValue)) {
        userDataDetailsMapper.deleteByUserId(userId, type);
      } else {
        value = 0;
        finalCollect.forEach(x -> {
          boolean b = modUserDataDetailsList.stream()
              .anyMatch(m -> m.getBoardId().equals(x.getId()));
          if (b) {
            value++;
          }
        });

        if (finalCollect.size() <= indexValue || value != modUserDataDetailsList.size()) {
          userDataDetailsMapper.deleteByUserId(userId, type);
        } else {
          log.info("setDefaultIndex_指标未变化");
          return;
        }
      }
      if (collect.size() > indexValue) {
        collect = collect.subList(0, indexValue);
      }
    }
    List<ModUserDataDetails> list = new ArrayList<>();
    AtomicInteger a = new AtomicInteger(1);
    for (ModuleMenuVo moduleMenuVo : collect) {
      ModUserDataDetails modUserDataDetails = ModUserDataDetails.builder()
          .boardId(moduleMenuVo.getId())
          .userId(userId)
          .sort(a.getAndIncrement())
          .type(type)
          .createTime(new Date())
          .updateTime(new Date())
          .build();
      list.add(modUserDataDetails);
    }
    if (list.size() == 0) {
      log.info("setDefaultIndex_没有指标_type={}_userId={}", type, userId);
      return;
    }
    userDataDetailsMapper.insertBatch(list);
  }

  /**
   * 首页数据查询
   * @param type
   * @param userId
   * @return
   */
  private HomeDataInfoVo findHomeData(String type,Integer userId,List<ModuleMenuVo> collect){
    HomeDataInfoVo homeDataInfoVo = new HomeDataInfoVo();
    try {
      //个人指标
      List<ModuleMenuVo> personalDetailList = userDataDetailsMapper.findPersonalDetailList(type,userId);
      if (personalDetailList.isEmpty()){
        log.info("findHomeData_没有指标_{}_{}",type,userId);
        return homeDataInfoVo;
      }
      homeDataInfoVo.setShowFlag(collect.size() > 9);
      //数据最新日期
      Date maxDate = dwsTradeRevenueIncDayDao.findMaxDate();
      List<String> codes = new ArrayList<>();
      ModHotelInfo modHotelInfo = hotelInfoCommonService.findHotelInfo(ThreadLocalHelper.getCompanyId());
      if (HotelTypeEnum.HOTEL.name().equals(type)){
        //酒店code 加入list  和集团共用一个dao
        codes.add(modHotelInfo.getHotelCode());
      }
      homeDataInfoVo.setBizData(DateUtils.dateToString(maxDate,DateUtils.DATE_FORMAT_YYYY_MM_DD));
      DataIndexVo ads = dataBoardIndexMapper.findHomeDataBoardByAds(type, codes, null, null, null,
          homeDataInfoVo.getBizData(), homeDataInfoVo.getBizData(), "NO").get(0);
      DataIndexVo md = dataBoardIndexMapper.queryMemberDataDetailForDataBoard(type, codes, null,
          null, null, homeDataInfoVo.getBizData(), homeDataInfoVo.getBizData(), "NO").get(0);
      DataIndexVo comment = dataBoardIndexMapper.queryCommentDataDetailForDataBoard(type, codes,
          null, null, null, homeDataInfoVo.getBizData(), homeDataInfoVo.getBizData(), "NO").get(0);
      DataIndexVo rate = dataBoardIndexMapper.findHomeDataBoardByrate(type, codes, null, null, null,
          homeDataInfoVo.getBizData(), homeDataInfoVo.getBizData(), "NO").get(0);

      List<HomeDataDetailsVo> list = new ArrayList<>();
      List<String> finalCodes = codes;
      personalDetailList.forEach(x ->{
        HomeDataDetailsVo vo = HomeDataDetailsVo.builder()
            .code(x.getModuleCode()).name(x.getModuleName()).desc(x.getDescName())
            .sort(x.getSort()).url(
                sxeUrl + "/sxe/groupDataDetail?pCode=" + x.getPCode() + "&code=" + x.getModuleCode()
                    + "&hotelId=" + modHotelInfo.getHotelId() + "&hotelCode="
                    + modHotelInfo.getHotelCode())
            .unit(x.getUnit()).build();
        if ("hotelMemberNightContributeRate".equals(x.getModuleCode()) || "blocMemberNightContributeRate".equals(x.getModuleCode())){
          vo.setValue(ads.getMemberNightContributeRate());
        }
        if ("hotelMemberRoomRevenue".equals(x.getModuleCode()) || "blocMemberRoomRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getMemberRoomRevenue());
        }
        if ("hotelGrossRevenue".equals(x.getModuleCode())||"blocGrossRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getTotalAmt());
        }
        if ("hotelRoomRevenue".equals(x.getModuleCode())||"blocRoomRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getRoomAmt());
        }
        if ("hotelCateringRevenue".equals(x.getModuleCode())||"blocCateringRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getCateringAmt());
        }
        if ("hotelOtherRevenue".equals(x.getModuleCode())||"blocOtherRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getOtherAmt());
        }
        if ("hotelInvestOCC".equals(x.getModuleCode())||"blocInvestOCC".equals(x.getModuleCode())){
          vo.setValue(ads.getInvestOCC());
        }
        if ("hotelOperationOCC".equals(x.getModuleCode())||"blocOperationOCC".equals(x.getModuleCode())){
          vo.setValue(ads.getDayOCC());
        }
        if ("hotelInvestADR".equals(x.getModuleCode())||"blocInvestADR".equals(x.getModuleCode())){
          vo.setValue(ads.getInvestARD());
        }
        if ("hotelOperationADR".equals(x.getModuleCode())||"blocOperationADR".equals(x.getModuleCode())){
          vo.setValue(ads.getDayADR());
        }
        if ("hotelInvestRevPAR".equals(x.getModuleCode())||"blocInvestRevPAR".equals(x.getModuleCode())){
          vo.setValue(ads.getInvestRevParMetrics());
        }
        if ("hotelOperationRevPAR".equals(x.getModuleCode())||"blocOperationRevPAR".equals(x.getModuleCode())){
          vo.setValue(ads.getDayRevPAR());
        }
        if ("hotelFaceToFace".equals(x.getModuleCode())||"blocFaceToFace".equals(x.getModuleCode())){
          vo.setValue(ads.getMemberF2F());
        }
        if ("hotelCorporateMember".equals(x.getModuleCode())||"blocCorporateMember".equals(x.getModuleCode())){
          vo.setValue(ads.getEntMemberNum());
        }
        if ("hotelAppDownloads".equals(x.getModuleCode())||"blocAppDownloads".equals(x.getModuleCode())){
          vo.setValue(ads.getAppDownloadN());
        }
        if ("hotelGiftBagSales".equals(x.getModuleCode())||"blocGiftBagSales".equals(x.getModuleCode())){
          vo.setValue(ads.getGiftSale());
        }
        if ("hotelRoomNightNum".equals(x.getModuleCode())||"blocRoomNightNum".equals(x.getModuleCode())){
          vo.setValue(ads.getChannelWebN());
        }
        if ("hotelBdxRoomNightNum".equals(x.getModuleCode())||"blocBdxRoomNightNum".equals(x.getModuleCode())){
          vo.setValue(ads.getBdxRoom());
        }
        if ("hotelSqRoomNightNum".equals(x.getModuleCode())||"blocSqRoomNightNum".equals(x.getModuleCode())){
          vo.setValue(ads.getSqRoom());
        }
        if ("hotelIncentiveOrder".equals(x.getModuleCode())||"blocIncentiveOrder".equals(x.getModuleCode())){
          vo.setValue(ads.getExcitationOrderRoom());
        }
        if ("hotelMotivateOther".equals(x.getModuleCode())||"blocMotivateOther".equals(x.getModuleCode())){
          vo.setValue(ads.getExcitationOtherOrderRoom());
        }
        if ("hotelActivate".equals(x.getModuleCode())||"blocActivate".equals(x.getModuleCode())){
          vo.setValue(ads.getHotelActiveEmpN());
        }
        if ("hotelActivationRate".equals(x.getModuleCode())||"blocActivationRate".equals(x.getModuleCode())){
          vo.setValue(ads.getActiveRate());
        }
        if ("hotelBdxRoomNightLeaseRate".equals(x.getModuleCode())||"blocBdxRoomNightLeaseRate".equals(x.getModuleCode())){
          vo.setValue(ads.getBdxRoomNightLeaseRate());
        }
        if ("hotelSqRoomNightLeaseRate".equals(x.getModuleCode())||"blocSqRoomNightLeaseRate".equals(x.getModuleCode())){
          vo.setValue(ads.getSqRoomNightLeaseRate());
        }
        if ("hotelRoomNightVolumeRate".equals(x.getModuleCode())||"blocRoomNightVolumeRate".equals(x.getModuleCode())){
          vo.setValue(ads.getRoomNightVolumeRate());
        }
        if ("hotelRoomNightLeaseRate".equals(x.getModuleCode())||"blocRoomNightLeaseRate".equals(x.getModuleCode())){
          vo.setValue(ads.getRoomNightLeaseRate());
        }
        if ("hotelBdxRoomNightVolumeRate".equals(x.getModuleCode())||"blocBdxRoomNightVolumeRate".equals(x.getModuleCode())){
          vo.setValue(ads.getBdxRoomNightVolumeRate());
        }
        if ("hotelSqRoomNightVolumeRate".equals(x.getModuleCode())||"blocSqRoomNightVolumeRate".equals(x.getModuleCode())){
          vo.setValue(ads.getSqRoomNightVolumeRate());
        }
        if ("hotelMotivateOtherRate".equals(x.getModuleCode())||"blocMotivateOtherRate".equals(x.getModuleCode())){
          vo.setValue(ads.getMotivateOtherRate());
        }
        // 会员规模-累计总人数
        if (hotelMemberScale.name().equals(x.getModuleCode())||blocMemberScale.name().equals(x.getModuleCode())){
          vo.setValue(md.getTotalMemDevelopeN());
        }
        // 法宝核销数
        if (hotelMagicWriteOff.name().equals(x.getModuleCode())||blocMagicWriteOff.name().equals(x.getModuleCode())){
          vo.setValue(md.getMagicWriteOff());
        }
        if ("hotelAdditionalCommentAverageScore".equals(x.getModuleCode())||"blocAdditionalCommentAverageScore".equals(x.getModuleCode())){
          vo.setValue(comment.getCurrdayScoreN());
        }
        if ("hotelAdditionalCommentVolume".equals(x.getModuleCode())||"blocAdditionalCommentVolume".equals(x.getModuleCode())){
          vo.setValue(comment.getCurrdayCommentN());
        }
        if (blocNewOpinionNum.name().equals(x.getModuleCode())||hotelNewOpinionNum.name().equals(x.getModuleCode())){
          vo.setValue(comment.getCurrdayOpinionN());
        }
        if (blocNewCommentOpinionNum.name().equals(x.getModuleCode())||hotelNewCommentOpinionNum.name().equals(x.getModuleCode())){
          vo.setValue(comment.getCurrdayCommentOpinionN());
        }
        //新增单房网评量 计算
        if ("hotelAdditionalCommentSingleRoom".equals(x.getModuleCode())||"blocAdditionalCommentSingleRoom".equals(x.getModuleCode())){
          vo.setValue(new BigDecimal(comment.getCurrdayCommentN()).divide(new BigDecimal(ads.getRoomValidNum()),2,
              RoundingMode.HALF_UP).toString());
        }
        if ("hotelBdxPagePoints".equals(x.getModuleCode())||"blocBdxPagePoints".equals(x.getModuleCode())){
          vo.setValue(comment.getBdx());
        }
        if (hotelCheckInWithMatchedMagicNum.name().equals(x.getModuleCode())||blocCheckInWithMatchedMagicNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getCheckInWithMatchedMagicNum());
        }
        if (hotelMagicWeaponUsageMemberNum.name().equals(x.getModuleCode())||blocMagicWeaponUsageMemberNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getCheckInWithMatchedMagicMemberNum());
        }
        if (hotelSqPagePoints.name().equals(x.getModuleCode())||blocSqPagePoints.name().equals(x.getModuleCode())){
          vo.setValue(comment.getGw());
        }
        if ("hotelMtPagePoints".equals(x.getModuleCode())||"blocMtPagePoints".equals(x.getModuleCode())){
          vo.setValue(comment.getMeiTuan());
        }
        if ("hotelXcPagePoints".equals(x.getModuleCode())||"blocXcPagePoints".equals(x.getModuleCode())){
          vo.setValue(comment.getXieCheng());
        }
      if ("hotelSupplyChainMoney".equals(x.getModuleCode())||"blocSupplyChainMoney".equals(x.getModuleCode())){
          vo.setValue(rate.getSupplyChainMoney());
        }
        if ("hotelUniformRecoveryRate".equals(x.getModuleCode())||"blocUniformRecoveryRate".equals(x.getModuleCode())){
          vo.setValue(rate.getUniformRecoveryRate());
        }
        //v4 新增指标
        if ("blocGWRoomRevenue".equals(x.getModuleCode())||"hotelGWRoomRevenue".equals(x.getModuleCode())){
          vo.setValue(ads.getGwRoomRevenue());
        }
        if ("hotelCorporateMemberNum".equals(x.getModuleCode())){
          vo.setValue(ads.getEnterpriseMember());
        }
        if (blocConsumerMemberAddScale.name().equals(x.getModuleCode())||hotelConsumerMemberAddScale.name().equals(x.getModuleCode())){
          vo.setValue(md.getConsumerNewMember());
        }
        if (blocConsumerMemberScale.name().equals(x.getModuleCode())||hotelConsumerMemberScale.name().equals(x.getModuleCode())){
//          DataIndexVo consumerMember = dataBoardIndexMapper.findConsumerMember(type, finalCodes,null,null,null,
//              homeDataInfoVo.getBizData(),homeDataInfoVo.getBizData(), "NO","DAY").get(0);
          vo.setValue(ads.getConsumerMember());
        }
        if (blocProduceTimeValue.name().equals(x.getModuleCode())||hotelProduceTimeValue.name().equals(x.getModuleCode())){
          vo.setValue(md.getProduceTimeValue());
        }
        if (blocConsumeTimeValue.name().equals(x.getModuleCode())||hotelConsumeTimeValue.name().equals(x.getModuleCode())){
          vo.setValue(md.getConsumeTimeValue());
        }
        if (blocBdxCheckInNum.name().equals(x.getModuleCode())||hotelBdxCheckInNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getBdxCheckInNum());
        }
        if (blocMemberCheckInAsCustomerNum.name().equals(x.getModuleCode())||hotelMemberCheckInAsCustomerNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getMemberCheckInAsCustomerNum());
        }
        if (blocCheckInWithMatchedMagicMemberNum.name().equals(x.getModuleCode())||hotelCheckInWithMatchedMagicMemberNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getCheckInWithMatchedMagicMemberNum());
        }
        if (blocCheckInWithMatchedMagicNum.name().equals(x.getModuleCode())||hotelCheckInWithMatchedMagicMemberNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getCheckInWithMatchedMagicNum());
        }
        if (blocMagicWeaponUsageMemberNum.name().equals(x.getModuleCode())||hotelCheckInWithMatchedMagicMemberNum.name().equals(x.getModuleCode())){
          vo.setValue(md.getMagicWeaponUsageMemberNum());
        }
        if ("blocOTAOnlineRoomNightNum".equals(x.getModuleCode())||"hotelOTAOnlineRoomNightNum".equals(x.getModuleCode())){
          vo.setValue(ads.getOtaOnlineRoomNight());
        }
        if ("blocOTAOnlineRate".equals(x.getModuleCode())||"hotelOTAOnlineRate".equals(x.getModuleCode())){
          vo.setValue(ads.getOtaOnlineRate());
        }
        if ("blocCtripOnlineRate".equals(x.getModuleCode())||"hotelCtripOnlineRate".equals(x.getModuleCode())){
          vo.setValue(ads.getCtripOnlineRate());
        }
        if ("blocMeituanOnlineRate".equals(x.getModuleCode())||"hotelMeituanOnlineRate".equals(x.getModuleCode())){
          vo.setValue(ads.getMeituanOnlineRate());
        }
        if ("blocTaobaoOnlineRate".equals(x.getModuleCode())||"hotelTaobaoOnlineRate".equals(x.getModuleCode())){
          vo.setValue(ads.getFeizhuOnlineRate());
        }
        if ("blocOtherOTAOnlineRate".equals(x.getModuleCode())||"hotelOtherOTAOnlineRate".equals(x.getModuleCode())){
          vo.setValue(ads.getOtherOnlineRate());
        }
        list.add(vo);
      });
      homeDataInfoVo.setHomeDataDetailsVoList(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return homeDataInfoVo;
  }

  @Override
  public List<DataSummaryVo> dataSummaryList(DataSummaryBo dataSummaryBo) {

    List<DataSummaryVo> vos = new ArrayList<>();
    Integer userId = ThreadLocalHelper.getUser().getId();
    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    try {
      // 获取用户的所有权限
      List<ModuleMenuVo> moduleMenuDetailList = modNewDataBoardMapper.findModuleMenuDetailList(dataSummaryBo.getType(),userRoles,userId);
      if (moduleMenuDetailList.isEmpty()){
        return vos;
      }
      //本期数据
      DataIndexVo nowAds = dataBoardIndexMapper.findHomeDataBoardByAds(dataSummaryBo.getType(), dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
          dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(), dataSummaryBo.getStartTime(), dataSummaryBo.getEndTime(), "NO").get(0);

      //对比
      String contrastStartTime = dataSummaryBo.getStartTime().replace(dataSummaryBo.getStartTime().substring(0, 4),dataSummaryBo.getContrastTime());
      String contrastEndTime = dataSummaryBo.getEndTime().replace(dataSummaryBo.getEndTime().substring(0, 4),dataSummaryBo.getContrastTime());
      DataIndexVo contrastAds = dataBoardIndexMapper.findHomeDataBoardByAds(dataSummaryBo.getType(),dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
          dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(),contrastStartTime,contrastEndTime, "NO").get(0);

      Map<Integer, List<ModuleMenuVo>> listMap = moduleMenuDetailList.stream().collect(groupingBy(ModuleMenuVo::getPId));
      //得到父类
      List<ModuleMenuVo> pBoard = listMap.get(0).stream().sorted(Comparator.comparing(ModuleMenuVo::getSort)).collect(Collectors.toList());
      pBoard.forEach(p ->{
        //营业额
        if (BLOC_TURNOVER.toString().equals(p.getModuleCode())
            || HOTEL_TURNOVER.toString().equals(p.getModuleCode())) {
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          moduleMenuVos.forEach(x -> {
            String hotelCode = DEFAULT_HOTEL_CODE;
            if (DataTypeEnum.HOTEL.toString().equals(dataSummaryBo.getType())) {
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                .unit(x.getUnit()).url(
                    sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code="
                        + x.getModuleCode()
                        + "&hotelCode=" + hotelCode).build();
            if ("hotelGrossRevenue".equals(x.getModuleCode()) || "blocGrossRevenue".equals(
                x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getTotalAmt());
              vo.setContrastData(contrastAds.getTotalAmt());
              vo.setRate(getRate(nowAds.getTotalAmt(), contrastAds.getTotalAmt()));
              detailedVoList.add(vo);

            }
            if ("hotelRoomRevenue".equals(x.getModuleCode()) || "blocRoomRevenue".equals(
                x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getRoomAmt());
              vo.setContrastData(contrastAds.getRoomAmt());
              vo.setRate(getRate(nowAds.getRoomAmt(), contrastAds.getRoomAmt()));
              detailedVoList.add(vo);
            }
            if ("hotelCateringRevenue".equals(x.getModuleCode()) || "blocCateringRevenue".equals(
                x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getCateringAmt());
              vo.setContrastData(contrastAds.getCateringAmt());
              vo.setRate(getRate(nowAds.getCateringAmt(), contrastAds.getCateringAmt()));
              detailedVoList.add(vo);
            }
            if ("hotelOtherRevenue".equals(x.getModuleCode()) || "blocOtherRevenue".equals(
                x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getOtherAmt());
              vo.setContrastData(contrastAds.getOtherAmt());
              vo.setRate(getRate(nowAds.getOtherAmt(), contrastAds.getOtherAmt()));
              detailedVoList.add(vo);
            }
            if ("blocGWRoomRevenue".equals(x.getModuleCode()) || "hotelGWRoomRevenue".equals(
                x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getGwRoomRevenue());
              vo.setContrastData(contrastAds.getGwRoomRevenue());
              vo.setRate(getRate(nowAds.getGwRoomRevenue(), contrastAds.getGwRoomRevenue()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //预算完成率
        if (HOTEL_BUDGET_FINISH_RATE.toString().equals(p.getModuleCode())
            || BLOC_BUDGET_FINISH_RATE.toString().equals(p.getModuleCode())) {
          try {
            if (!DateTypeEnum.DAY.name().equals(dataSummaryBo.getTimeType()) && !DateTypeEnum.WEEK.name().equals(dataSummaryBo.getTimeType())){
              String nowStartTime = DateUtils.dateToString(DateUtils.strConverDate(dataSummaryBo.getStartTime()), DateUtils.DATE_FORMAT_YYYY_MM);
              String nowEndTime = DateUtils.dateToString(DateUtils.strConverDate(dataSummaryBo.getEndTime()), DateUtils.DATE_FORMAT_YYYY_MM);
              //对比
              String contrastStartDate = DateUtils.dateToString(DateUtils.strConverDate(contrastStartTime), DateUtils.DATE_FORMAT_YYYY_MM);
              String contrastEndDate = DateUtils.dateToString(DateUtils.strConverDate(contrastEndTime), DateUtils.DATE_FORMAT_YYYY_MM);

              DataIndexVo nowFinishRate = dataBoardIndexMapper.findHomeDataBoardByFinishRate(dataSummaryBo.getType(),dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
                  dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(),nowStartTime,nowEndTime,"NO").get(0);
              DataIndexVo contrastFinishRate = dataBoardIndexMapper.findHomeDataBoardByFinishRate(dataSummaryBo.getType(),dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
                  dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(),contrastStartDate,contrastEndDate,"NO").get(0);

              List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
              List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
              moduleMenuVos.forEach(x ->{
                String hotelCode = "000001";
                if ("HOTEL".equals(dataSummaryBo.getType())){
                  // 填充hotelCode给前端用
                  hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                      dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
                }
                DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                    .unit(x.getUnit()).url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code=" + x.getModuleCode()
                        + "&hotelCode="+ hotelCode).build();
                if ("blocGrossRevenueBudget".equals(x.getModuleCode())||"hotelGrossRevenueBudget".equals(x.getModuleCode())){
                      vo.setCurrentPeriodData(nowFinishRate.getTotalAmtFinishRate());
                      vo.setContrastData(contrastFinishRate.getTotalAmtFinishRate());
                      vo.setRate(getRate(nowFinishRate.getTotalAmtFinishRate(),contrastFinishRate.getTotalAmtFinishRate()));
                  detailedVoList.add(vo);
                }
                if ("blocRoomRevenueBudget".equals(x.getModuleCode())||"hotelRoomRevenueBudget".equals(x.getModuleCode())){
                      vo.setCurrentPeriodData(nowFinishRate.getRoomAmtFinishRate());
                      vo.setContrastData(contrastFinishRate.getRoomAmtFinishRate());
                      vo.setRate(getRate(nowFinishRate.getRoomAmtFinishRate(),contrastFinishRate.getRoomAmtFinishRate()));
                  detailedVoList.add(vo);
                }
                if ("blocCateringRevenueBudget".equals(x.getModuleCode())||"hotelCateringRevenueBudget".equals(x.getModuleCode())){
                      vo.setCurrentPeriodData(nowFinishRate.getCateringAmtFinishRate());
                      vo.setContrastData(contrastFinishRate.getCateringAmtFinishRate());
                      vo.setRate(getRate(nowFinishRate.getCateringAmtFinishRate(),contrastFinishRate.getCateringAmtFinishRate()));
                  detailedVoList.add(vo);
                }
              });
              DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName()).unit(p.getUnit())
                  .detailedVoList(detailedVoList).build();
              vos.add(dataSummaryVo);
            }
          } catch (ParseException e) {
            log.error("完成率异常 ", e);
          }
        }
        //经营数据
        if (BLOC_BUSINESS_DATA.toString().equals(p.getModuleCode())
            || HOTEL_BUSINESS_DATA.toString().equals(p.getModuleCode())) {
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());

          moduleMenuVos.forEach(x ->{
            String hotelCode = "000001";
            if ("HOTEL".equals(dataSummaryBo.getType())){
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                .unit(x.getUnit()).url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code=" + x.getModuleCode()
                    + "&hotelCode="+ hotelCode).build();
            if ("hotelInvestOCC".equals(x.getModuleCode())||"blocInvestOCC".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getInvestOCC());
                  vo.setContrastData(contrastAds.getInvestOCC());
                  vo.setRate(getRate(nowAds.getInvestOCC(),contrastAds.getInvestOCC()));
              detailedVoList.add(vo);
            }
            if ("hotelOperationOCC".equals(x.getModuleCode())||"blocOperationOCC".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getDayOCC());
                  vo.setContrastData(contrastAds.getDayOCC());
                  vo.setRate(getRate(nowAds.getDayOCC(),contrastAds.getDayOCC()));
              detailedVoList.add(vo);
            }
            if ("hotelOperationADR".equals(x.getModuleCode())||"blocOperationADR".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getDayADR());
                  vo.setContrastData(contrastAds.getDayADR());
                  vo.setRate(getRate(nowAds.getDayADR(),contrastAds.getDayADR()));
              detailedVoList.add(vo);
            }
            if ("hotelInvestADR".equals(x.getModuleCode())||"blocInvestADR".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getInvestARD());
                  vo.setContrastData(contrastAds.getInvestARD());
                  vo.setRate(getRate(nowAds.getInvestARD(),contrastAds.getInvestARD()));
              detailedVoList.add(vo);
            }
            if ("hotelOperationRevPAR".equals(x.getModuleCode())||"blocOperationRevPAR".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getDayRevPAR());
                  vo.setContrastData(contrastAds.getDayRevPAR());
                  vo.setRate(getRate(nowAds.getDayRevPAR(),contrastAds.getDayRevPAR()));
              detailedVoList.add(vo);
            }
            if ("hotelInvestRevPAR".equals(x.getModuleCode())||"blocInvestRevPAR".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getInvestRevParMetrics());
                  vo.setContrastData(contrastAds.getInvestRevParMetrics());
                  vo.setRate(getRate(nowAds.getInvestRevParMetrics(),contrastAds.getInvestRevParMetrics()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName()).unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //会员营销
        if (BLOC_MEMBER_MARKETING.toString().equals(p.getModuleCode())
            || HOTEL_MEMBER_MARKETING.toString().equals(p.getModuleCode())) {
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          moduleMenuVos.forEach(x ->{
            String hotelCode = "000001";
            if ("HOTEL".equals(dataSummaryBo.getType())){
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                .unit(x.getUnit()).url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code=" + x.getModuleCode()
                    + "&hotelCode="+ hotelCode).build();
            if ("hotelFaceToFace".equals(x.getModuleCode())||"blocFaceToFace".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getMemberF2F());
                  vo.setContrastData(contrastAds.getMemberF2F());
                  vo.setRate(getRate(nowAds.getMemberF2F(),contrastAds.getMemberF2F()));
              detailedVoList.add(vo);
            }
            if ("hotelCorporateMember".equals(x.getModuleCode())||"blocCorporateMember".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getEntMemberNum());
                  vo.setContrastData(contrastAds.getEntMemberNum());
                  vo.setRate(getRate(nowAds.getEntMemberNum(),contrastAds.getEntMemberNum()));
              detailedVoList.add(vo);
            }
            if ("hotelMemberNightContributeRate".equals(x.getModuleCode()) || "blocMemberNightContributeRate".equals(x.getModuleCode())){
                vo.setCurrentPeriodData(nowAds.getMemberNightContributeRate());
                vo.setContrastData(contrastAds.getMemberNightContributeRate());
                vo.setRate(getRate(nowAds.getMemberNightContributeRate(), contrastAds.getMemberNightContributeRate()));
                detailedVoList.add(vo);
            }
            if ("hotelMemberRoomRevenue".equals(x.getModuleCode()) || "blocMemberRoomRevenue".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getMemberRoomRevenue());
              vo.setContrastData(contrastAds.getMemberRoomRevenue());
              vo.setRate(getRate(nowAds.getMemberRoomRevenue(), contrastAds.getMemberRoomRevenue()));
              detailedVoList.add(vo);
            }
            if ("hotelAppDownloads".equals(x.getModuleCode())||"blocAppDownloads".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getAppDownloadN());
                  vo.setContrastData(contrastAds.getAppDownloadN());
                  vo.setRate(getRate(nowAds.getAppDownloadN(),contrastAds.getAppDownloadN()));
              detailedVoList.add(vo);
            }
            if ("hotelGiftBagSales".equals(x.getModuleCode())||"blocGiftBagSales".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getGiftSale());
                  vo.setContrastData(contrastAds.getGiftSale());
                  vo.setRate(getRate(nowAds.getGiftSale(),contrastAds.getGiftSale()));
              detailedVoList.add(vo);
            }
            if ("hotelRoomNightNum".equals(x.getModuleCode())||"blocRoomNightNum".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getChannelWebN());
                  vo.setContrastData(contrastAds.getChannelWebN());
                  vo.setRate(getRate(nowAds.getChannelWebN(),contrastAds.getChannelWebN()));
              detailedVoList.add(vo);
            }
            if ("hotelBdxRoomNightNum".equals(x.getModuleCode())||"blocBdxRoomNightNum".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getBdxRoom());
                  vo.setContrastData(contrastAds.getBdxRoom());
                  vo.setRate(getRate(nowAds.getBdxRoom(),contrastAds.getBdxRoom()));
              detailedVoList.add(vo);
            }
            if ("hotelSqRoomNightNum".equals(x.getModuleCode())||"blocSqRoomNightNum".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getSqRoom());
                  vo.setContrastData(contrastAds.getSqRoom());
                  vo.setRate(getRate(nowAds.getSqRoom(),contrastAds.getSqRoom()));
              detailedVoList.add(vo);
            }
            if ("hotelIncentiveOrder".equals(x.getModuleCode())||"blocIncentiveOrder".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getExcitationOrderRoom());
                  vo.setContrastData(contrastAds.getExcitationOrderRoom());
                  vo.setRate(getRate(nowAds.getExcitationOrderRoom(),contrastAds.getExcitationOrderRoom()));
              detailedVoList.add(vo);
            }
            if ("hotelMotivateOther".equals(x.getModuleCode())||"blocMotivateOther".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getExcitationOtherOrderRoom());
                  vo.setContrastData(contrastAds.getExcitationOtherOrderRoom());
                  vo.setRate(getRate(nowAds.getExcitationOtherOrderRoom(),contrastAds.getExcitationOtherOrderRoom()));
              detailedVoList.add(vo);
            }
            if ("hotelActivate".equals(x.getModuleCode())||"blocActivate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getHotelActiveEmpN());
                  vo.setContrastData(contrastAds.getHotelActiveEmpN());
                  vo.setRate(getRate(nowAds.getHotelActiveEmpN(),contrastAds.getHotelActiveEmpN()));
              detailedVoList.add(vo);
            }
            if ("hotelActivationRate".equals(x.getModuleCode())||"blocActivationRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getActiveRate());
                  vo.setContrastData(contrastAds.getActiveRate());
                  vo.setRate(getRate(nowAds.getActiveRate(),contrastAds.getActiveRate()));
              detailedVoList.add(vo);
            }
            if ("hotelRoomNightLeaseRate".equals(x.getModuleCode())||"blocRoomNightLeaseRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getRoomNightLeaseRate());
                  vo.setContrastData(contrastAds.getRoomNightLeaseRate());
                  vo.setRate(getRate(nowAds.getRoomNightLeaseRate(),contrastAds.getRoomNightLeaseRate()));
              detailedVoList.add(vo);
            }
            if ("hotelBdxRoomNightLeaseRate".equals(x.getModuleCode())||"blocBdxRoomNightLeaseRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getBdxRoomNightLeaseRate());
                  vo.setContrastData(contrastAds.getBdxRoomNightLeaseRate());
                  vo.setRate(getRate(nowAds.getBdxRoomNightLeaseRate(),contrastAds.getBdxRoomNightLeaseRate()));
              detailedVoList.add(vo);
            }
            if ("hotelSqRoomNightLeaseRate".equals(x.getModuleCode())||"blocSqRoomNightLeaseRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getSqRoomNightLeaseRate());
                  vo.setContrastData(contrastAds.getSqRoomNightLeaseRate());
                  vo.setRate(getRate(nowAds.getSqRoomNightLeaseRate(),contrastAds.getSqRoomNightLeaseRate()));
              detailedVoList.add(vo);
            }
            if ("hotelRoomNightVolumeRate".equals(x.getModuleCode())||"blocRoomNightVolumeRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getRoomNightVolumeRate());
                  vo.setContrastData(contrastAds.getRoomNightVolumeRate());
                  vo.setRate(getRate(nowAds.getRoomNightVolumeRate(),contrastAds.getRoomNightVolumeRate()));
              detailedVoList.add(vo);
            }
            if ("hotelBdxRoomNightVolumeRate".equals(x.getModuleCode())||"blocBdxRoomNightVolumeRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getBdxRoomNightVolumeRate());
                  vo.setContrastData(contrastAds.getBdxRoomNightVolumeRate());
                  vo.setRate(getRate(nowAds.getBdxRoomNightVolumeRate(),contrastAds.getBdxRoomNightVolumeRate()));
              detailedVoList.add(vo);
            }
            if ("hotelSqRoomNightVolumeRate".equals(x.getModuleCode())||"blocSqRoomNightVolumeRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getSqRoomNightVolumeRate());
                  vo.setContrastData(contrastAds.getSqRoomNightVolumeRate());
                  vo.setRate(getRate(nowAds.getSqRoomNightVolumeRate(),contrastAds.getSqRoomNightVolumeRate()));
              detailedVoList.add(vo);
            }
            if ("hotelMotivateOtherRate".equals(x.getModuleCode())||"blocMotivateOtherRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowAds.getMotivateOtherRate());
                  vo.setContrastData(contrastAds.getMotivateOtherRate());
                  vo.setRate(getRate(nowAds.getSqRoomNightVolumeRate(),contrastAds.getSqRoomNightVolumeRate()));
              detailedVoList.add(vo);
            }
            if ("hotelCorporateMemberNum".equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getEnterpriseMember());
              vo.setContrastData(contrastAds.getEnterpriseMember());
              vo.setRate(getRate(nowAds.getEnterpriseMember(), contrastAds.getEnterpriseMember()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName()).unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //会员数据
        if (BLOC_MEMBER_DATA.toString().equals(p.getModuleCode())
            || HOTEL_MEMBER_DATA.toString().equals(p.getModuleCode())) {
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          DataIndexVo currentMemberData = dataBoardIndexMapper.queryMemberData(
                  dataSummaryBo.getType(),
                  dataSummaryBo.getHotelCodes(),
                  dataSummaryBo.getStartTime(),
                  dataSummaryBo.getEndTime());
          DataIndexVo contrastMemberData = dataBoardIndexMapper.queryMemberData(
                  dataSummaryBo.getType(),
                  dataSummaryBo.getHotelCodes(),
                  contrastStartTime,
                  contrastEndTime);
          moduleMenuVos.forEach(x -> {
            String hotelCode = DEFAULT_HOTEL_CODE;
            if (DataTypeEnum.HOTEL.toString().equals(dataSummaryBo.getType())) {
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder()
                .moduleName(x.getModuleName())
                .unit(x.getUnit())
                .url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() +
                    "&code=" + x.getModuleCode() + "&hotelCode=" + hotelCode)
                .build();
            // 会员发展规模
            if (hotelMemberScale.toString().equals(x.getModuleCode())
                || blocMemberScale.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getTotalMemDevelopeN());
              vo.setContrastData(contrastMemberData.getTotalMemDevelopeN());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getTotalMemDevelopeN(),
                  contrastMemberData.getTotalMemDevelopeN()));
              detailedVoList.add(vo);
            }
            // 法宝核销数
            if (hotelMagicWriteOff.toString().equals(x.getModuleCode())
                || blocMagicWriteOff.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getMagicWriteOff());
              vo.setContrastData(contrastMemberData.getMagicWriteOff());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getMagicWriteOff(), contrastMemberData.getMagicWriteOff()));
              detailedVoList.add(vo);
            }
            // 会员消费新增
            if (blocConsumerMemberAddScale.toString().equals(x.getModuleCode())
                || hotelConsumerMemberAddScale.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getConsumerNewMember());
              vo.setContrastData(contrastMemberData.getConsumerNewMember());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getConsumerNewMember(),
                  contrastMemberData.getConsumerNewMember()));
              detailedVoList.add(vo);
            }
            // 会员消费总量
            if (blocConsumerMemberScale.toString().equals(x.getModuleCode())
                || hotelConsumerMemberScale.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(nowAds.getConsumerMember());
              vo.setContrastData(contrastAds.getConsumerMember());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowAds.getConsumerMember(),
                  contrastAds.getConsumerMember()));
              detailedVoList.add(vo);
            }
            // 产出时光值
            if (blocProduceTimeValue.toString().equals(x.getModuleCode())
                || hotelProduceTimeValue.equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getProduceTimeValue());
              vo.setContrastData(contrastMemberData.getProduceTimeValue());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getProduceTimeValue(),
                  contrastMemberData.getProduceTimeValue()));
              detailedVoList.add(vo);
            }
            // 时光消耗值
            if (blocConsumeTimeValue.toString().equals(x.getModuleCode())
                || hotelConsumeTimeValue.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getConsumeTimeValue());
              vo.setContrastData(contrastMemberData.getConsumeTimeValue());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getConsumeTimeValue(),
                  contrastMemberData.getConsumeTimeValue()));
              detailedVoList.add(vo);
            }
            // 百达星系入住人次
            if (blocBdxCheckInNum.toString().equals(x.getModuleCode())
                || hotelBdxCheckInNum.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getBdxCheckInNum());
              vo.setContrastData(contrastMemberData.getBdxCheckInNum());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getBdxCheckInNum(),
                  contrastMemberData.getBdxCheckInNum()));
              detailedVoList.add(vo);
            }
            // 入住人为会员人次
            if (blocMemberCheckInAsCustomerNum.toString().equals(x.getModuleCode())
                || hotelMemberCheckInAsCustomerNum.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getMemberCheckInAsCustomerNum());
              vo.setContrastData(contrastMemberData.getMemberCheckInAsCustomerNum());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getMemberCheckInAsCustomerNum(),
                  contrastMemberData.getMemberCheckInAsCustomerNum()));
              detailedVoList.add(vo);
            }
            // 持有适用法宝人次
            if (blocCheckInWithMatchedMagicMemberNum.toString().equals(x.getModuleCode())
                || hotelCheckInWithMatchedMagicMemberNum.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getCheckInWithMatchedMagicMemberNum());
              vo.setContrastData(contrastMemberData.getCheckInWithMatchedMagicMemberNum());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getCheckInWithMatchedMagicMemberNum(),
                  contrastMemberData.getCheckInWithMatchedMagicMemberNum()));
              detailedVoList.add(vo);
            }
            // 持有适用法宝量
            if (blocCheckInWithMatchedMagicNum.toString().equals(x.getModuleCode())
                || hotelCheckInWithMatchedMagicNum.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getCheckInWithMatchedMagicNum());
              vo.setContrastData(contrastMemberData.getCheckInWithMatchedMagicNum());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getCheckInWithMatchedMagicNum(),
                  contrastMemberData.getCheckInWithMatchedMagicNum()));
              detailedVoList.add(vo);
            }
            // 使用法宝人次
            if (blocMagicWeaponUsageMemberNum.toString().equals(x.getModuleCode())
                || hotelMagicWeaponUsageMemberNum.toString().equals(x.getModuleCode())) {
              vo.setCurrentPeriodData(currentMemberData.getMagicWeaponUsageMemberNum());
              vo.setContrastData(contrastMemberData.getMagicWeaponUsageMemberNum());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(currentMemberData.getMagicWeaponUsageMemberNum(),
                  contrastMemberData.getMagicWeaponUsageMemberNum()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName())
              .unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //OTA营销数据
        if (BLOC_OTA_MARKETING.toString().equals(p.getModuleCode())
            || HOTEL_OTA_MARKETING.toString().equals(p.getModuleCode())) {
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          moduleMenuVos.forEach(x ->{
            String hotelCode = "000001";
            if ("HOTEL".equals(dataSummaryBo.getType())){
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                .unit(x.getUnit()).url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code=" + x.getModuleCode()
                    + "&hotelCode="+ hotelCode).build();
            if ("blocOTAOnlineRoomNightNum".equals(x.getModuleCode())||"hotelOTAOnlineRoomNightNum".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getOtaOnlineRoomNight());
              vo.setContrastData(contrastAds.getOtaOnlineRoomNight());
              vo.setRate(getRate(nowAds.getOtaOnlineRoomNight(),contrastAds.getOtaOnlineRoomNight()));
              detailedVoList.add(vo);
            }
            if ("blocOTAOnlineRate".equals(x.getModuleCode())||"hotelOTAOnlineRate".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getOtaOnlineRate());
              vo.setContrastData(contrastAds.getOtaOnlineRate());
              vo.setRate(getRate(nowAds.getOtaOnlineRate(),contrastAds.getOtaOnlineRate()));
              detailedVoList.add(vo);
            }
            if ("blocCtripOnlineRate".equals(x.getModuleCode())||"hotelCtripOnlineRate".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getCtripOnlineRate());
              vo.setContrastData(contrastAds.getCtripOnlineRate());
              vo.setRate(getRate(nowAds.getCtripOnlineRate(),contrastAds.getCtripOnlineRate()));
              detailedVoList.add(vo);
            }
            if ("blocMeituanOnlineRate".equals(x.getModuleCode())||"hotelMeituanOnlineRate".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getMeituanOnlineRate());
              vo.setContrastData(contrastAds.getMeituanOnlineRate());
              vo.setRate(getRate(nowAds.getMeituanOnlineRate(),contrastAds.getMeituanOnlineRate()));
              detailedVoList.add(vo);
            }
            if ("blocTaobaoOnlineRate".equals(x.getModuleCode())||"hotelTaobaoOnlineRate".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getFeizhuOnlineRate());
              vo.setContrastData(contrastAds.getFeizhuOnlineRate());
              vo.setRate(getRate(nowAds.getFeizhuOnlineRate(),contrastAds.getFeizhuOnlineRate()));
              detailedVoList.add(vo);
            }
            if ("blocOtherOTAOnlineRate".equals(x.getModuleCode())||"hotelOtherOTAOnlineRate".equals(x.getModuleCode())){
              vo.setCurrentPeriodData(nowAds.getOtherOnlineRate());
              vo.setContrastData(contrastAds.getOtherOnlineRate());
              vo.setRate(getRate(nowAds.getOtherOnlineRate(),contrastAds.getOtherOnlineRate()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName()).unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //美誉度
        if (BLOC_REPUTATION.toString().equals(p.getModuleCode())
            || HOTEL_REPUTATION.toString().equals(p.getModuleCode())) {
          // 1、查询所有数据
          DataIndexVo nowComment = dataBoardIndexMapper.queryCommentDataForDataBoard(
                  dataSummaryBo.getHotelCodes(),
                  dataSummaryBo.getBrandCodes(),
                  dataSummaryBo.getDivisionCodes(),
                  dataSummaryBo.getManagementCodes(),
                  dataSummaryBo.getStartTime(),
                  dataSummaryBo.getEndTime());
          DataIndexVo contrastComment = dataBoardIndexMapper.queryCommentDataForDataBoard(
                  dataSummaryBo.getHotelCodes(),
                  dataSummaryBo.getBrandCodes(),
                  dataSummaryBo.getDivisionCodes(),
                  dataSummaryBo.getManagementCodes(),
                  contrastStartTime,
                  contrastEndTime);
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          // 2、返回结构数据填充
          moduleMenuVos.forEach(module -> {
            String hotelCode = DEFAULT_HOTEL_CODE;
            if (DataTypeEnum.HOTEL.toString().equals(dataSummaryBo.getType())) {
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder()
                .moduleName(module.getModuleName())
                .unit(module.getUnit())
                .url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code="
                    + module.getModuleCode() + "&hotelCode=" + hotelCode)
                .build();
            // 2.1 新增点评均分
            if (hotelAdditionalCommentAverageScore.toString().equals(module.getModuleCode())
                || blocAdditionalCommentAverageScore.toString().equals(module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getCurrdayScoreN());
              vo.setContrastData(contrastComment.getCurrdayScoreN());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getCurrdayScoreN(),
                  contrastComment.getCurrdayScoreN()));
              detailedVoList.add(vo);
            }
            // 2.2 新增单房网评量 删除
            // 2.3 新增点评量
            if (hotelAdditionalCommentVolume.toString().equals(module.getModuleCode())
                || blocAdditionalCommentVolume.toString().equals(module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getCurrdayCommentN());
              vo.setContrastData(contrastComment.getCurrdayCommentN());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getCurrdayCommentN(),
                  contrastComment.getCurrdayCommentN()));
              detailedVoList.add(vo);
            }
            // 2.4 新增意见量
            if (hotelNewOpinionNum.name().equals(module.getModuleCode())
                || blocNewOpinionNum.name().equals(module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getCurrdayOpinionN());
              vo.setContrastData(contrastComment.getCurrdayOpinionN());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getCurrdayOpinionN(),
                  contrastComment.getCurrdayOpinionN()));
              detailedVoList.add(vo);
            }
            // 2.5 新增点评意见量
            if (hotelNewCommentOpinionNum.name().equals(module.getModuleCode())
                || blocNewCommentOpinionNum.name().equals(module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getCurrdayCommentOpinionN());
              vo.setContrastData(contrastComment.getCurrdayCommentOpinionN());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getCurrdayCommentOpinionN(),
                      contrastComment.getCurrdayCommentOpinionN()));
              detailedVoList.add(vo);
            }
            // 2.6 页面分-百达星系
            if (hotelBdxPagePoints.name().equals(module.getModuleCode())
                || blocBdxPagePoints.name().equals(
                module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getBdx());
              vo.setContrastData(contrastComment.getBdx());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getBdx(),
                  contrastComment.getBdx()));
              detailedVoList.add(vo);
            }
            // 2.7 页面分-商祺会 删除
            // 2.8 页面分-美团
            if (hotelMtPagePoints.toString().equals(module.getModuleCode())
                || blocMtPagePoints.toString().equals(
                module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getMeiTuan());
              vo.setContrastData(contrastComment.getMeiTuan());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getMeiTuan(), contrastComment.getMeiTuan()));
              detailedVoList.add(vo);
            }
            // 2.9 页面分-携程
            if (hotelXcPagePoints.toString().equals(module.getModuleCode())
                || blocXcPagePoints.toString().equals(
                module.getModuleCode())) {
              vo.setCurrentPeriodData(nowComment.getXieCheng());
              vo.setContrastData(contrastComment.getXieCheng());
              vo.setRate(BigDecimalUtils.calculateIncreaseRate(nowComment.getXieCheng(), contrastComment.getXieCheng()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName())
              .unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
        //采集
        if (BLOC_COLLECTION.toString().equals(p.getModuleCode())|| HOTEL_COLLECTION.toString().equals(p.getModuleCode())){
          DataIndexVo nowRate = dataBoardIndexMapper.findHomeDataBoardByrate(dataSummaryBo.getType(),dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
              dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(),dataSummaryBo.getStartTime(),dataSummaryBo.getEndTime(),"NO").get(0);
          DataIndexVo contrastRate = dataBoardIndexMapper.findHomeDataBoardByrate(dataSummaryBo.getType(),dataSummaryBo.getHotelCodes(), dataSummaryBo.getBrandCodes(),
              dataSummaryBo.getDivisionCodes(), dataSummaryBo.getManagementCodes(),contrastStartTime,contrastEndTime,"NO").get(0);
          List<DataSummaryDetailedVo> detailedVoList = new ArrayList<>();
          List<ModuleMenuVo> moduleMenuVos = listMap.get(p.getId());
          moduleMenuVos.forEach(x ->{
            String hotelCode = "000001";
            if ("HOTEL".equals(dataSummaryBo.getType())){
              // 填充hotelCode给前端用
              hotelCode = Objects.isNull(nowAds.getHotelCode()) ?
                  dataSummaryBo.getHotelCodes().get(0) : nowAds.getHotelCode();
            }
            DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().moduleName(x.getModuleName())
                .unit(x.getUnit()).url(sxeUrl + "/sxe/groupDataDetail?pCode=" + p.getModuleCode() + "&code=" + x.getModuleCode()
                    + "&hotelCode="+ hotelCode).build();
            if ("hotelSupplyChainMoney".equals(x.getModuleCode())||"blocSupplyChainMoney".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowRate.getSupplyChainMoney());
                  vo.setContrastData(contrastRate.getSupplyChainMoney());
                  vo.setRate(getRate(nowRate.getSupplyChainMoney(),contrastRate.getSupplyChainMoney()));
              detailedVoList.add(vo);
            }
            if ("hotelUniformRecoveryRate".equals(x.getModuleCode())||"blocUniformRecoveryRate".equals(x.getModuleCode())){
                  vo.setCurrentPeriodData(nowRate.getUniformRecoveryRate());
                  vo.setContrastData(contrastRate.getUniformRecoveryRate());
                  vo.setRate(getRate(nowRate.getUniformRecoveryRate(),contrastRate.getUniformRecoveryRate()));
              detailedVoList.add(vo);
            }
          });
          DataSummaryVo dataSummaryVo = DataSummaryVo.builder().moduleName(p.getModuleName()).unit(p.getUnit())
              .detailedVoList(detailedVoList).build();
          vos.add(dataSummaryVo);
        }
      });
    } catch (Exception e) {
      log.error("dataSummaryList_查询数据总数异常 ", e);
    }
    return vos;
  }

  /**
   * @param dataBulletinBoardBo
   * @return
   * 思路：根据前端传过来的code 反射对象字段  查询结果将年去掉 直留月日 然后使用本期list和往期list使用格式化的时间一一对比
   */
  @Override
  public DataSummaryVo dataBulletinBoardHotelList(DataBulletinBoardBo dataBulletinBoardBo) {
    DataSummaryVo dataSummaryVo = DataSummaryVo.builder().build();
    List<DataSummaryDetailedVo> vos1 = new ArrayList<>();

    List<String> codes = new ArrayList<>();
      //酒店code 加入list  和集团共用一个dao
    try {
      codes.add(dataBulletinBoardBo.getHotelCode());
      //对比日期
      String contrastStartTime = dataBulletinBoardBo.getStartTime().replace(dataBulletinBoardBo.getStartTime().substring(0, 4),dataBulletinBoardBo.getContrastTime());
      String contrastEndTime = dataBulletinBoardBo.getEndTime().replace(dataBulletinBoardBo.getEndTime().substring(0, 4),dataBulletinBoardBo.getContrastTime());
      List<DataIndexVo> now = null;
      List<DataIndexVo> contrast = null;
      if ("md".equals(DataIndexListEnum.getType(dataBulletinBoardBo.getCode()))) {
        now = dataBoardIndexMapper.queryMemberDataDetailForDataBoard("HOTEL",
            codes,
            null,
            null,
            null,
            dataBulletinBoardBo.getStartTime(),
            dataBulletinBoardBo.getEndTime(),
            "YES");
        contrast = dataBoardIndexMapper.queryMemberDataDetailForDataBoard("HOTEL",
            codes,
            null,
            null,
            null,
            contrastStartTime,
            contrastEndTime,
            "YES");
      }

      if ("ads".equals(DataIndexListEnum.getType(dataBulletinBoardBo.getCode()))) {
        if (DataIndexListEnum.blocConsumerMemberScale.getDataCode().equals(DataIndexListEnum.getCode(dataBulletinBoardBo.getCode()))){
          List<DataIndexVo> consumerMember = adsTradeConsumerMemberMapper.findConsumerMember("HOTEL",
              codes, null, null,
              null, DateUtil.parse(dataBulletinBoardBo.getStartTime(), BaseConstants.FORMAT_DATE2), DateUtil.parse(dataBulletinBoardBo.getEndTime(), BaseConstants.FORMAT_DATE2), "YES",
              "DAY");
          if (consumerMember == null || consumerMember.isEmpty()){
            log.info("dataBulletinBoardHotelList数据为空_酒店会员消费指标_入参_{}",JSON.toJSONString(dataBulletinBoardBo));
            return dataSummaryVo;
          }
          consumerMember.forEach(x -> {
            DataSummaryDetailedVo detailedVo = DataSummaryDetailedVo.builder()
                .moduleName(x.getBizDate()).contrastData(x.getContrastConsumerMember()).currentPeriodData(x.getConsumerMember())
                .rate(x.getRate()).build();
            vos1.add(detailedVo);
          });
          dataSummaryVo.setDetailedVoList(vos1);
          return dataSummaryVo;
        }
        //本期
        now = dataBoardIndexMapper.findHomeDataBoardByAds("HOTEL",codes,null,null,null,dataBulletinBoardBo.getStartTime(),dataBulletinBoardBo.getEndTime(),"YES");
        //对比数
        contrast = dataBoardIndexMapper.findHomeDataBoardByAds("HOTEL",codes,null,null,null,contrastStartTime,contrastEndTime,"YES");
      }
      if ("finishRate".equals(DataIndexListEnum.getType(dataBulletinBoardBo.getCode()))){
        if (!DateTypeEnum.DAY.name().equals(dataBulletinBoardBo.getTimeType()) && !DateTypeEnum.WEEK.name().equals(dataBulletinBoardBo.getTimeType())){
          try {
            String nowStartTime = DateUtils.dateToString(DateUtils.strConverDate(dataBulletinBoardBo.getStartTime()), DateUtils.DATE_FORMAT_YYYY_MM);
            String nowEndTime = DateUtils.dateToString(DateUtils.strConverDate(dataBulletinBoardBo.getEndTime()), DateUtils.DATE_FORMAT_YYYY_MM);
            //对比
            String contrastStartDate = DateUtils.dateToString(DateUtils.strConverDate(contrastStartTime), DateUtils.DATE_FORMAT_YYYY_MM);
            String contrastEndDate = DateUtils.dateToString(DateUtils.strConverDate(contrastEndTime), DateUtils.DATE_FORMAT_YYYY_MM);

            now = dataBoardIndexMapper.findHomeDataBoardByFinishRate("HOTEL",codes,null,null,null,nowStartTime,nowEndTime,"YES");
            contrast= dataBoardIndexMapper.findHomeDataBoardByFinishRate("HOTEL",codes,null,null,null,contrastStartDate,contrastEndDate,"YES");
          } catch (ParseException e) {
            log.error(e.getMessage(), e);
          }
        }
      }
      if ("comment".equals(DataIndexListEnum.getType(dataBulletinBoardBo.getCode()))){
        now = dataBoardIndexMapper.queryCommentDataDetailForDataBoard("HOTEL",codes,null,null,null,dataBulletinBoardBo.getStartTime(),dataBulletinBoardBo.getEndTime(),"YES");
        contrast = dataBoardIndexMapper.queryCommentDataDetailForDataBoard("HOTEL",codes,null,null,null,contrastStartTime,contrastEndTime,"YES");
      }
      if ("rate".equals(DataIndexListEnum.getType(dataBulletinBoardBo.getCode()))) {
        now = dataBoardIndexMapper.findHomeDataBoardByrate("HOTEL",codes, null, null,null, dataBulletinBoardBo.getStartTime(), dataBulletinBoardBo.getEndTime(), "YES");
        contrast = dataBoardIndexMapper.findHomeDataBoardByrate("HOTEL",codes, null,null, null, contrastStartTime, contrastEndTime, "YES");
      }
      List<DataIndexVo> finalContrast = contrast;
      if (now == null || now.isEmpty()){
        log.info("dataBulletinBoardHotelList数据为空_入参_{}",JSON.toJSONString(dataBulletinBoardBo));
        return dataSummaryVo;
      }
      now.forEach(x -> {
        String contrastValue = "0";
        DataIndexVo dataIndexVo;
        if (!finalContrast.isEmpty()) {
          //然后使用本期list和往期list使用格式化的时间一一对比
          List<DataIndexVo> collect = finalContrast.stream().filter(y -> x.getFormatDate().equals(y.getFormatDate())).collect(Collectors.toList());
          if (!collect.isEmpty()){
            dataIndexVo = collect.get(0);
            //对比数
            Object contrastByKey = getValueByKey(dataIndexVo, DataIndexListEnum.getCode(dataBulletinBoardBo.getCode()));
            contrastValue = null != contrastByKey ? contrastByKey.toString() : "0";
          }
        }
        //本期数
        Object nowByKey = getValueByKey(x, DataIndexListEnum.getCode(dataBulletinBoardBo.getCode()));
        String nowValue = null != nowByKey ? nowByKey.toString() : "0";
        DataSummaryDetailedVo detailedVo = DataSummaryDetailedVo.builder()
            .moduleName(x.getBizDate()).contrastData(contrastValue).currentPeriodData(nowValue)
            .rate(BigDecimalUtils.calculateIncreaseRate(nowValue, contrastValue)).build();
        vos1.add(detailedVo);
      });
      dataSummaryVo.setDetailedVoList(vos1);
    } catch (Exception e) {
      log.error("dataBulletinBoardHotelList_入参 " + JSON.toJSONString(dataBulletinBoardBo), e);
    }
    return dataSummaryVo;
  }

  /**
   * 替换目标日期（yyyy-MM-dd）的年份
   *
   * @param timeStr 替换目标
   * @param contrastYear 替换年份
   * @return 替换结果
   */
  private String getContrastTime(String timeStr, String contrastYear) {
    return timeStr.replace(timeStr.substring(0, 4), contrastYear);
  }

  @Override
  public DataSummaryVo dataBulletinBoardBlocList(DataBulletinBlocBoardBo dataBulletinBlocBoardBo) {
    List<DataSummaryDetailedVo> detailedVos = new ArrayList<>();
    DataSummaryVo dataSummaryVo = DataSummaryVo.builder().build();
    try {
      //对比日期
      String contrastStartTime = getContrastTime(dataBulletinBlocBoardBo.getStartTime(),
          dataBulletinBlocBoardBo.getContrastTime());
      String contrastEndTime = getContrastTime(dataBulletinBlocBoardBo.getEndTime(),
          dataBulletinBlocBoardBo.getContrastTime());
      if (!"blocAdditionalCommentSingleRoom".equals(dataBulletinBlocBoardBo.getCode())) {
        List<DataInfoVo> now = null;
        List<DataInfoVo> contrast = null;
        //合计list
        List<DataInfoVo> nowDivision = null;
        List<DataInfoVo> contrastDivision = null;
        if ("md".equals(DataIndexListEnum.getType(dataBulletinBlocBoardBo.getCode()))) {
          now = dataBoardIndexMapper.queryDataBoardMemberData(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //对比数据
          contrast = dataBoardIndexMapper.queryDataBoardMemberData(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //事业部合计
          nowDivision = dataBoardIndexMapper.queryDataBoardMemberData(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
          //对比数据
          contrastDivision = dataBoardIndexMapper.queryDataBoardMemberData(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
        }
        if ("ads".equals(DataIndexListEnum.getType(dataBulletinBlocBoardBo.getCode()))) {
          if ("consumerMember".equals(DataIndexListEnum.getCode(dataBulletinBlocBoardBo.getCode()))){
            List<DataInfoVo> consumerMember = dataBoardIndexMapper.findConsumerMemberV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
                dataBulletinBlocBoardBo.getTimeType());
            if (consumerMember == null || consumerMember.isEmpty()) {
              log.info("dataBulletinBoardBlocList_会员消费指标_{}_数据为空_{}", dataBulletinBlocBoardBo.getCode(),
                  JSON.toJSONString(dataBulletinBlocBoardBo));
              return dataSummaryVo;
            }
            Map<String, List<DataInfoVo>> listMap = consumerMember.stream().collect(groupingBy(DataInfoVo::getDepartmentCode));
            listMap.forEach((k,value) ->{
              List<DataSummaryDetailedVo> vos = new ArrayList<>();
              //酒店明细
              value.forEach(x ->{
                DataSummaryDetailedVo detailedVo = DataSummaryDetailedVo.builder()
                    .moduleName(x.getHotelName()).hotelCode(x.getHotelCode())
                    .contrastData(x.getContrastValue())
                    .currentPeriodData(x.getValue())
                    .rate(getRate(x.getValue(), x.getContrastValue())).build();
                vos.add(detailedVo);
                key = x.getDepartmentName();
              });
              //事业部合计
              String sum = String.valueOf(value.stream().mapToInt(s -> Integer.parseInt(s.getValue())).reduce(Integer::sum).getAsInt());
              String conSum = String.valueOf(value.stream().mapToInt(s -> Integer.parseInt(s.getContrastValue())).reduce(Integer::sum).getAsInt());
              DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().detailedVoList(vos)
                  .moduleName(key).currentPeriodData(sum).contrastData(conSum).rate(getRate(sum, conSum)).build();
              detailedVos.add(vo);
            });
            dataSummaryVo.setDetailedVoList(detailedVos);
            return dataSummaryVo;
          }
            now = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.HOTEL.name());
            //对比数据
            contrast = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                contrastStartTime, contrastEndTime,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.HOTEL.name());
            //事业部合计
            nowDivision = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.DIVISION.name());
            //对比数据
            contrastDivision = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                contrastStartTime, contrastEndTime,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.DIVISION.name());
        }
        if ("rate".equals(DataIndexListEnum.getType(dataBulletinBlocBoardBo.getCode()))) {
          now = dataBoardIndexMapper.findHomeDataBlocBoardByrateV2(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //对比数据
          contrast = dataBoardIndexMapper.findHomeDataBlocBoardByrateV2(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //事业部合计
          nowDivision = dataBoardIndexMapper.findHomeDataBlocBoardByrateV2(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
          //对比数据
          contrastDivision = dataBoardIndexMapper.findHomeDataBlocBoardByrateV2(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
        }
        if ("comment".equals(DataIndexListEnum.getType(dataBulletinBlocBoardBo.getCode()))) {
          now = dataBoardIndexMapper.queryDataBoardMemberDetail(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //对比数据
          contrast = dataBoardIndexMapper.queryDataBoardMemberDetail(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.HOTEL.name());
          //事业部合计
          nowDivision = dataBoardIndexMapper.queryDataBoardMemberDetail(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
          //对比数据
          contrastDivision = dataBoardIndexMapper.queryDataBoardMemberDetail(
              dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
              dataBulletinBlocBoardBo.getDivisionCodes(),
              dataBulletinBlocBoardBo.getManagementCodes(),
              contrastStartTime, contrastEndTime,
              dataBulletinBlocBoardBo.getCode(),
              GroupTypeEnum.DIVISION.name());
        }
        if ("finishRate".equals(DataIndexListEnum.getType(dataBulletinBlocBoardBo.getCode()))) {
          if (!DateTypeEnum.DAY.name().equals(dataBulletinBlocBoardBo.getTimeType())
              && !DateTypeEnum.WEEK.name().equals(dataBulletinBlocBoardBo.getTimeType())) {
            String nowStartTime = DateUtils.dateToString( DateUtils.strConverDate(dataBulletinBlocBoardBo.getStartTime()),DateUtils.DATE_FORMAT_YYYY_MM);
            String nowEndTime = DateUtils.dateToString(DateUtils.strConverDate(dataBulletinBlocBoardBo.getEndTime()),DateUtils.DATE_FORMAT_YYYY_MM);
            //对比
            String contrastStartDate = DateUtils.dateToString(DateUtils.strConverDate(contrastStartTime), DateUtils.DATE_FORMAT_YYYY_MM);
            String contrastEndDate = DateUtils.dateToString(DateUtils.strConverDate(contrastEndTime), DateUtils.DATE_FORMAT_YYYY_MM);
            now = dataBoardIndexMapper.findHomeDataBlocBoardByFinishRateV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                nowStartTime, nowEndTime,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.HOTEL.name());
            //对比数据
            contrast = dataBoardIndexMapper.findHomeDataBlocBoardByFinishRateV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                contrastStartDate, contrastEndDate,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.HOTEL.name());
            //事业部合计
            nowDivision = dataBoardIndexMapper.findHomeDataBlocBoardByFinishRateV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                nowStartTime, nowEndTime,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.DIVISION.name());
            //对比数据
            contrastDivision = dataBoardIndexMapper.findHomeDataBlocBoardByFinishRateV2(
                dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
                dataBulletinBlocBoardBo.getDivisionCodes(),
                dataBulletinBlocBoardBo.getManagementCodes(),
                contrastStartDate, contrastEndDate,
                dataBulletinBlocBoardBo.getCode(),
                GroupTypeEnum.DIVISION.name());
          }
        }
        if (now == null || now.isEmpty()) {
          log.info("dataBulletinBoardBlocList_{}_数据为空_{}", dataBulletinBlocBoardBo.getCode(),
              JSON.toJSONString(dataBulletinBlocBoardBo));
          return dataSummaryVo;
        }
        Map<String, List<DataInfoVo>> listMap = now.stream().filter(vo -> Objects.nonNull(vo.getDepartmentCode())).collect(groupingBy(DataInfoVo::getDepartmentCode));
        Map<String, List<DataInfoVo>> contrastMap = contrast.stream().filter(vo -> Objects.nonNull(vo.getDepartmentCode())).collect(groupingBy(DataInfoVo::getDepartmentCode));
        List<DataInfoVo> finalNowDivision = nowDivision;
        List<DataInfoVo> finalContrastDivision = contrastDivision;
        listMap.forEach((k, value) -> {
          List<DataSummaryDetailedVo> vos = new ArrayList<>();
          //获取对比数据的对应的事业部数据
          List<DataInfoVo> dataInfoVos = contrastMap.get(k);
          //遍历各事业部详细数据
          value.forEach(x -> {
            String contrastValue = "0.00";
            if (dataInfoVos != null && !dataInfoVos.isEmpty()) {
              //筛选出对立数据和本期数据酒店相同的数据
              List<DataInfoVo> collect = dataInfoVos.stream()
                  .filter(y -> x.getHotelCode().equals(y.getHotelCode()))
                  .collect(Collectors.toList());
              if (!collect.isEmpty()) {
                DataInfoVo dataInfoVo = collect.get(0);
                //对比数
                contrastValue = null != dataInfoVo.getValue() ? dataInfoVo.getValue() : "0.00";
              }
            }
            DataSummaryDetailedVo detailedVo = DataSummaryDetailedVo.builder()
                .moduleName(x.getHotelName()).hotelCode(x.getHotelCode())
                .contrastData(contrastValue)
                .currentPeriodData(x.getValue())
                .rate(BigDecimalUtils.calculateIncreaseRate(x.getValue(), contrastValue)).build();
            vos.add(detailedVo);
            key = x.getDepartmentName();
          });
          //本期事业部
          List<DataInfoVo> collect = finalNowDivision.stream()
              .filter(x -> k.equals(x.getDepartmentCode())).collect(Collectors.toList());
          List<DataInfoVo> collectV2 = finalContrastDivision.stream()
              .filter(x -> k.equals(x.getDepartmentCode())).collect(Collectors.toList());
          String v = "0.00";
          String v2 = "0.00";
          if (!collect.isEmpty()) {
            DataInfoVo dataInfoVo = collect.get(0);
            //事业部合计对比数
            v = null != dataInfoVo.getValue() ? dataInfoVo.getValue() : "0.00";
          }
          if (!collectV2.isEmpty()) {
            DataInfoVo dataInfoVo = collectV2.get(0);
            //事业部合计对比数
            v2 = null != dataInfoVo.getValue() ? dataInfoVo.getValue() : "0.00";
          }
          DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder().detailedVoList(vos)
              .moduleName(key).currentPeriodData(v).contrastData(v2).rate(BigDecimalUtils.calculateIncreaseRate(v, v2)).build();
          detailedVos.add(vo);
        });
        dataSummaryVo.setDetailedVoList(detailedVos);
      }else  {
        // 1.本期ads有效物理房
        List<DataInfoVo> nowAds = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
            dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
            dataBulletinBlocBoardBo.getDivisionCodes(), dataBulletinBlocBoardBo.getManagementCodes(),
            dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
            "roomValidNum",GroupTypeEnum.HOTEL.name());
        // 2. 对比起ads有效物理房
        List<DataInfoVo> contrastAds = dataBoardIndexMapper.findHomeDataBlocBoardByAdsV2(
            dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
            dataBulletinBlocBoardBo.getDivisionCodes(), dataBulletinBlocBoardBo.getManagementCodes(),
            contrastStartTime, contrastEndTime,"roomValidNum",GroupTypeEnum.HOTEL.name());
        //网评
        List<DataInfoVo> nowComment= dataBoardIndexMapper.findHomeDataBlocBoardByCommentV2(
            dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
            dataBulletinBlocBoardBo.getDivisionCodes(), dataBulletinBlocBoardBo.getManagementCodes(),
            dataBulletinBlocBoardBo.getStartTime(), dataBulletinBlocBoardBo.getEndTime(),
            "blocAdditionalCommentVolume",GroupTypeEnum.HOTEL.name());
        //对比数据
        List<DataInfoVo> contrastComment= dataBoardIndexMapper.findHomeDataBlocBoardByCommentV2(
            dataBulletinBlocBoardBo.getHotelCodes(), dataBulletinBlocBoardBo.getBrandCodes(),
            dataBulletinBlocBoardBo.getDivisionCodes(), dataBulletinBlocBoardBo.getManagementCodes(),
            contrastStartTime, contrastEndTime,"blocAdditionalCommentVolume",GroupTypeEnum.HOTEL.name());
        Map<String, List<DataInfoVo>> listCommentMap = nowComment.stream().collect(groupingBy(DataInfoVo::getDepartmentCode));
        Map<String, List<DataInfoVo>> contrastCommentMap = contrastComment.stream().collect(groupingBy(DataInfoVo::getDepartmentCode));
        Map<String, List<DataInfoVo>> listAdsMap = nowAds.stream().collect(groupingBy(DataInfoVo::getDepartmentCode));
        Map<String, List<DataInfoVo>> contrastAdsMap = contrastAds.stream().collect(groupingBy(DataInfoVo::getDepartmentCode));

        listCommentMap.forEach((k,value) -> {
          List<DataSummaryDetailedVo> vos = new ArrayList<>();
          List<DataInfoVo> dataIndexCommentVos = contrastCommentMap.get(k);
          List<DataInfoVo> dataIndexAdsVos = listAdsMap.get(k);
          List<DataInfoVo> dataIndexAdsVos2 = contrastAdsMap.get(k);
          value.forEach(x ->{
            String contrastValue = "0";
            if (dataIndexCommentVos != null && dataIndexCommentVos.size() > 0){
              //往期相同酒店list
              List<DataInfoVo> collect = dataIndexCommentVos.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList());
              if (!collect.isEmpty()){
                //往期相同酒店
                contrastValue =  collect.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList()).get(0).getValue();
              }
            }
            String nowAdsValue = "0";
            if (dataIndexAdsVos != null && dataIndexAdsVos.size() > 0){
              //本期相同酒店ads
              List<DataInfoVo> collect1 = dataIndexAdsVos.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList());
              if (!collect1.isEmpty()){
                //本期相同酒店ads
                nowAdsValue = collect1.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList()).get(0).getValue();
              }
            }
            String contrastAdsValue = "0";
            if (dataIndexAdsVos2 !=null && dataIndexAdsVos2.size() > 0){
              //往期相同酒店ads
              List<DataInfoVo> collect2 = dataIndexAdsVos2.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList());
              if (!collect2.isEmpty()){
                //往期相同酒店ads
                contrastAdsValue = collect2.stream().filter(y -> x.getHotelCode().equals(y.getHotelCode())).collect(Collectors.toList()).get(0).getValue();
              }
            }

            DataSummaryDetailedVo detailedVo = DataSummaryDetailedVo.builder()
                .moduleName(x.getHotelName()).hotelCode(x.getHotelCode()).contrastData( "0".equals(contrastAdsValue) ? "0.00" :
                    new BigDecimal(contrastValue).divide(new BigDecimal(contrastAdsValue), 2,
                        RoundingMode.HALF_UP).toString())
                .currentPeriodData( "0".equals(nowAdsValue) ? "0.00" :
                    new BigDecimal(x.getValue()).divide(new BigDecimal(nowAdsValue), 2,
                        RoundingMode.HALF_UP).toString()).build();
            detailedVo.setRate(getRate(detailedVo.getCurrentPeriodData(),detailedVo.getContrastData()));
            vos.add(detailedVo);
            key = x.getDepartmentName();
          });
          DataSummaryDetailedVo vo = DataSummaryDetailedVo.builder()
              .moduleName(key).detailedVoList(vos).build();
//          vo.setRate(getRate(vo.getCurrentPeriodData(),vo.getContrastData()));
          detailedVos.add(vo);
        });
        dataSummaryVo.setDetailedVoList(detailedVos);
      }
    } catch (Exception e) {
      log.error("dataBulletinBoardBlocList_入参 " + JSON.toJSON(dataBulletinBlocBoardBo), e);
    }

    return dataSummaryVo;
  }

  public static <T> Object getValueByKey(T t, String key) {
    Class clazz = t.getClass();
    Field[] fields = clazz.getDeclaredFields();
    Field resultField = Arrays.stream(fields)
        .filter(field -> field.getName().equals(key))
        .findFirst()
        .get();
    Object obj = null;
    resultField.setAccessible(true);
    try {
      obj = resultField.get(t);
    } catch (IllegalArgumentException | IllegalAccessException e) {
      log.error(e.getMessage(), e);
    }
    return obj;
  }

  /**
   * 对比率
   * @param newData 本期数
   * @param contrast 对比数
   */
  private static String getRate(String newData, String contrast){
    newData = newData == null ? "0" : newData;
    contrast = contrast == null ? "0" : contrast;
    if (BigDecimal.ZERO.compareTo(new BigDecimal(contrast)) == 0 || contrast.isEmpty()){
      return "0";
    }
    return (new BigDecimal(newData).subtract(new BigDecimal(contrast))).divide(
        new BigDecimal(contrast),2,RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0,RoundingMode.DOWN).toString();
  }

}