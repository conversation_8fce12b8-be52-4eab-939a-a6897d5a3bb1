package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.date.DateUtil;
import com.delonix.bi.dao.mapper.AdsTradeConsumerMemberMapper;
import com.google.common.collect.Lists;
import com.shands.mod.dao.model.datarevision.po.AdsTradeConsumerMember;
import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.enums.DeptDataEnum;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.statistics.BdwCardStatisticService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
@Slf4j
public class BdwCardStatisticServiceImpl implements BdwCardStatisticService {


    @Autowired
    private HotelInfoCommonService hotelInfoCommonService;

    @Autowired
    private AdsTradeConsumerMemberMapper adsTradeConsumerMemberMapper;


    @Override
    public List<DeptDataVo> queryBdwCardDeptData(DeptDataReq deptDataReq) {
        // TODO 权限
        List<DeptDataVo> res = Lists.newArrayList();
        ModHotelInfo modHotelInfo = hotelInfoCommonService.findHotelInfo(ThreadLocalHelper.getCompanyId());

        String hotelCode =  modHotelInfo.getHotelCode();
        Date stDate = DateUtil.parse(deptDataReq.getStartTime(), BaseConstants.FORMAT_DATE2);
        Date edDate = DateUtil.parse(deptDataReq.getEndTime(), BaseConstants.FORMAT_DATE2);
        String type = deptDataReq.getType();

        List<DeptDataVo> deptData = Lists.newArrayList();

        if (DeptDataEnum.HOTEL_VOUCHER.name().equals(type)) {
            List<AdsTradeConsumerMember> adsTradeConsumerMembers = adsTradeConsumerMemberMapper.queryAdsTradeConsumer(
                    hotelCode, stDate, edDate);
            Map<String, List<AdsTradeConsumerMember>> consumerMemberListMap = adsTradeConsumerMembers.stream()
                    .collect(groupingBy(AdsTradeConsumerMember::getUcId));
            List<DeptDataVo> tempDeptData = Lists.newArrayList();
            consumerMemberListMap.forEach((key, value) -> {
                DeptDataVo dataVo = new DeptDataVo();
                long consumerMemberNum = value.stream().mapToLong(AdsTradeConsumerMember::getConsumerMemberNum).sum();
                long consumerMemberNum2 = value.stream().mapToLong(AdsTradeConsumerMember::getConsumerMemberNum).sum();
                dataVo.setDeptName(value.get(0).getDeptName());
                dataVo.setKey(value.get(0).getStaffName());
                dataVo.setFirstValueNum(BigDecimal.valueOf(consumerMemberNum));
                dataVo.setSecondValueNum(BigDecimal.valueOf(consumerMemberNum2));
                dataVo.setFirstValue(String.valueOf(consumerMemberNum));
                dataVo.setSecondValue(String.valueOf(consumerMemberNum2));
                tempDeptData.add(dataVo);
            });
            deptData = tempDeptData;
        } else if (DeptDataEnum.APP_DISCOUNT_VOUCHER.name().equals(type)) {


        }
        //按部门分组
        Map<String, List<DeptDataVo>> deptNameMap = deptData.stream().collect(groupingBy(DeptDataVo::getDeptName));

        for (String deptName : deptNameMap.keySet()) {
            // 部门内排序
            List<DeptDataVo> deptDataVos = deptNameMap.get(deptName).stream()
                    .sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                            .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            DeptDataVo vo = new DeptDataVo();
            vo.setKey(deptName);
            BigDecimal firstValueNum = deptDataVos.stream().map(DeptDataVo::getFirstValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal secondValueNum = deptDataVos.stream().map(DeptDataVo::getSecondValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setFirstValueNum(firstValueNum);
            vo.setSecondValueNum(secondValueNum);
            vo.setFirstValue(firstValueNum.toString());
            vo.setSecondValue(secondValueNum.toString());
            vo.setChild(deptDataVos);
            res.add(vo);
        }
        // 部门级排序
        res = res.stream().sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder())).collect(Collectors.toList());

        // calc total
        if (CollectionUtils.isNotEmpty(res)) {
            DeptDataVo totalDataVo = new DeptDataVo();
            totalDataVo.setKey("合计");
            totalDataVo.setFirstValue(res.stream().map(DeptDataVo::getFirstValueNum).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
            totalDataVo.setFirstValue(res.stream().map(DeptDataVo::getSecondValueNum).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
            res.add(0, totalDataVo);
        }
        return res;

    }
}
