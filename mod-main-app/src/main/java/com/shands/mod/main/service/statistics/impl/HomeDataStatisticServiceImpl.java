package com.shands.mod.main.service.statistics.impl;

import com.shands.mod.dao.mapper.hs.ModMessageLogMapper;
import com.shands.mod.dao.model.statistics.vo.BillNotifyVo;
import com.shands.mod.dao.model.v0701.pojo.ModMessageLog;
import com.shands.mod.main.service.message.MessageStateEnum;
import com.shands.mod.main.service.message.MessageTypeEnum;
import com.shands.mod.main.service.statistics.HomeDataStatisticService;
import com.shands.mod.main.util.ThreadLocalHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description: 首页数据统计服务实现类
 * @Author: mazhiyong
 */
@Service
@Slf4j
public class HomeDataStatisticServiceImpl implements HomeDataStatisticService {

    @Autowired
    private ModMessageLogMapper modMessageLogMapper;

    @Override
    public BillNotifyVo queryBillNotifyLast() {
        BillNotifyVo result = new BillNotifyVo();
        
        try {
            // 获取当前用户的最新未读账单消息
            ModMessageLog messageLog = new ModMessageLog();
            messageLog.setReadState(MessageStateEnum.READ_UN.getStateCode()); // 未读状态
            messageLog.setRecipientId(String.valueOf(ThreadLocalHelper.getUser().getId())); // 当前用户ID
            messageLog.setMessageType(MessageTypeEnum.MESSAGE_TYPE_APP.getTypeCode()); // APP消息类型
            messageLog.setSendState(MessageStateEnum.SEND_SUCCESS.getStateCode()); // 发送成功状态
            
            // 查询未读消息列表
            List<ModMessageLog> unreadMessages = modMessageLogMapper.queryMessageByStatu(messageLog);
            
            // 过滤出账单消息（标题包含"结算账单"）
            ModMessageLog billMessage = null;
            if (!CollectionUtils.isEmpty(unreadMessages)) {
                for (ModMessageLog msg : unreadMessages) {
                    if (msg.getMessageTitle() != null && msg.getMessageTitle().contains("结算账单")) {
                        billMessage = msg;
                        break; // 获取第一条账单消息
                    }
                }
            }
            
            if (billMessage != null) {
                // 有未读的账单消息
                result.setShowFlag(true);
                result.setNotifyMsg(billMessage.getMessageContent());
                result.setId(String.valueOf(billMessage.getId()));
                log.info("找到未读账单消息，用户ID: {}, 消息ID: {}", ThreadLocalHelper.getUser().getId(), billMessage.getId());
            } else {
                // 没有未读的账单消息
                result.setShowFlag(false);
                result.setNotifyMsg("暂无新的账单通知");
                result.setId(null);
                log.info("未找到未读账单消息，用户ID: {}", ThreadLocalHelper.getUser().getId());
            }
            
        } catch (Exception e) {
            log.error("查询账单通知失败，用户ID: {}", ThreadLocalHelper.getUser().getId(), e);
            result.setShowFlag(false);
            result.setNotifyMsg("查询账单通知失败");
            result.setId(null);
        }
        
        return result;
    }
}
