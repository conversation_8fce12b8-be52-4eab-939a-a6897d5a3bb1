package com.shands.mod.main.aspect;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.hs.RoomOperateLogMapper;
import com.shands.mod.dao.model.enums.RoomStatusEnum;
import com.shands.mod.dao.model.enums.RoomStatusUriEnum;
import com.shands.mod.dao.model.hs.RoomOperateLog;
import com.shands.mod.dao.model.v0701.dto.RoomLockDto;
import com.shands.mod.dao.model.v0701.dto.RoomStatusDto;
import com.shands.mod.dao.model.v0701.dto.RoomsDto;
import com.shands.mod.dao.model.v0701.dto.TmpRoomDto;
import com.shands.mod.main.annotation.CrsRoomStatus;
import com.shands.mod.main.util.ThreadLocalHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * crs房间状态切面
 *
 * <AUTHOR>
 * @date 2021/05/07
 */
@Slf4j
@Aspect
@Component
public class CrsRoomStatusAspect {


  private final RoomOperateLogMapper operateLogMapper;

  public CrsRoomStatusAspect(RoomOperateLogMapper operateLogMapper) {
    this.operateLogMapper = operateLogMapper;
  }

  /**
   * 切入点
   */
  @Pointcut("@annotation(com.shands.mod.main.annotation.CrsRoomStatus)")
  public void pointcut() {
  }

  @Around("pointcut() && @annotation(crsRoomStatus)")
  public Object doAround(ProceedingJoinPoint point, CrsRoomStatus crsRoomStatus) throws Throwable {
    Object obj = point.proceed();
    //请求接口地址
    RoomStatusUriEnum uriEnum = crsRoomStatus.ROOM_STATUS_URI_ENUM();

    Object[] args = point.getArgs();
    RoomStatusDto dto = new RoomStatusDto();
    TmpRoomDto tmpDto = new TmpRoomDto();

    try {
      for (Object arg : args) {
        if (arg instanceof RoomStatusDto) {
          dto = ((RoomStatusDto) arg);
          saveOperateLog(uriEnum, dto, obj);
          break;
        }

        if (arg instanceof  TmpRoomDto){
          tmpDto = ((TmpRoomDto) arg);
          saveOperateLog2(uriEnum, tmpDto, obj);
          break;
        }
      }

    }catch (Exception e){
      log.error("[房态日志记录异常] ", e);
    }

    return obj;
  }

  /**
   * 保存操作日志
   *
   * @param uriEnum uri枚举
   * @param dto     请求数据
   * @param res     请求结果
   */
  private void saveOperateLog(RoomStatusUriEnum uriEnum, RoomStatusDto dto, Object res) {
    RoomOperateLog operateLog = new RoomOperateLog();
    JSONObject json = (JSONObject) JSONObject.toJSON(res);
    buildData(operateLog, uriEnum, json);

    String username = operateLog.getOperateUsername();

    if (Objects.nonNull(dto)) {
      dto.setCompanyId(ThreadLocalHelper.getCompanyId());
      operateLog.setParams(JSONObject.toJSON(dto).toString());
    }

    StringBuilder context = new StringBuilder(username);
    switch (uriEnum) {
      case QUERY_ROOMSTA:
        context.append(json.getString("message"));
        context.append("查询了房态列表");
        break;

      case UPDATE_ROOMSTA:
        List<RoomsDto> rooms = dto.getRooms();
        context.append("修改了房号 ");
        for (RoomsDto room : rooms) {
          context.append("[ ").append(room.getRmno()).append(" ] ");
        }
        context.append("房态为“").append(RoomStatusEnum.conversion(dto.getNewSta())).append("” ");
        context.append(json.getString("message"));
        break;

      case LOCK_ROOMSTA:
        List<RoomLockDto> lockList = dto.getLockList();
        context.append("锁定了房间：");
        for (RoomLockDto lockDto : lockList) {
          context.append("[ ").append(lockDto.getRmno()).append(" ] ");
        }
        context.append(json.getString("message"));
        break;

      case UNLOCK_ROOMSTA:
        List<RoomLockDto> lockList1 = dto.getLockList();
        context.append("解锁了房间：");
        for (RoomLockDto lockDto : lockList1) {
          context.append("[ ").append(lockDto.getRmno()).append(" ] ");
        }
        context.append(json.getString("message"));
        break;

      case REPAIRE_ROOMSTA:
        List<RoomLockDto> repairList = dto.getRepairList();
        context.append("设置维修房间：");
        for (RoomLockDto room : repairList) {
          context.append("[ ").append(room.getRmno()).append(" ] ");
        }
        context.append(json.getString("message"));
        break;

      case REMOVE_REPAIREROOMSTA:
        List<RoomLockDto> repairList1 = dto.getRepairList();
        context.append("解除维修房间：");
        for (RoomLockDto room : repairList1) {
          context.append("[ ").append(room.getRmno()).append(" ] ");
        }
        context.append(json.getString("message"));
        break;

      default:
        context.delete(0, context.length());
        context.append("未获取到").append(username).append("的相关操作");
        break;
    }
    operateLog.setContext(context.toString());
    operateLogMapper.insert(operateLog);
  }

  private void saveOperateLog2(RoomStatusUriEnum uriEnum, TmpRoomDto dto, Object res){
    RoomOperateLog operateLog = new RoomOperateLog();
    JSONObject json = (JSONObject) JSONObject.toJSON(res);
    buildData(operateLog, uriEnum, json);

    String username = operateLog.getOperateUsername();

    if (Objects.nonNull(dto)) {
      dto.setCompanyId(ThreadLocalHelper.getCompanyId());
      operateLog.setParams(JSONObject.toJSON(dto).toString());
    }

    StringBuilder context = new StringBuilder(username);
    String[] rooms = dto.getRmno().split(",");
    List<String> list = new ArrayList<>();

    switch (uriEnum){
      case SET_ROOMS_TMP:
        context.append("设置了房号：");

        list = Arrays.asList(rooms);

        for (String rmno : list) {
          context.append("[ ").append(rmno).append(" ] ");
        }
        context.append("“临时态” ");

        if (json.getInteger("code") == 0){
          context.append("成功");
        }else {
          context.append("失败");
        }

        break;

      case CANCEL_ROOMS_TMP:

        context.append("取消了房号：");
        list = Arrays.asList(rooms);

        for (String rmno : list) {
          context.append("[ ").append(rmno).append(" ] ");
        }
        context.append("“临时态” ");

        if (json.getInteger("code") == 0){
          context.append("成功");
        }else {
          context.append("失败，");
          context.append(json.getString("message"));
        }
        break;

      default:
        context.delete(0, context.length());
        context.append("未获取到").append(username).append("的相关操作");
        break;
    }

    operateLog.setContext(context.toString());
    operateLogMapper.insert(operateLog);

  }

  private void buildData(RoomOperateLog operateLog, RoomStatusUriEnum uriEnum, JSONObject json){
    operateLog.setOperation(uriEnum.name());
    operateLog.setOperateUserid(ThreadLocalHelper.getUser().getId());
    String username = ThreadLocalHelper.getUser().getName();
    operateLog.setOperateUsername(username);
    operateLog.setCompanyId(ThreadLocalHelper.getCompanyId());
    operateLog.setCreateTime(new Date());

    String result = json.toJSONString();
    if (result.length() > 500) {
      result = result.substring(0, 500);
    }

    if (uriEnum != RoomStatusUriEnum.QUERY_ROOMSTA) {
      operateLog.setResult(result);
    } else {
      json.put("data", "");
      operateLog.setResult(result);
    }
  }
}
