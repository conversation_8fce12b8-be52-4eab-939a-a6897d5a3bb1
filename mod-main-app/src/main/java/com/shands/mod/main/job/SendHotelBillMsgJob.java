package com.shands.mod.main.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.delonix.bi.dao.mapper.AdsFinaDlEcoAppSettlementBillMMapper;
import com.delonix.bi.dao.model.AdsFinaDlEcoAppSettlementBillM;
import com.delonix.bi.dao.model.AdsFinaDlEcoAppSettlementBillMExample;
import com.shands.mod.dao.mapper.SettleBillFileRecordsMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.SettleBillFileRecords;
import com.shands.mod.dao.model.SettleBillFileRecordsExample;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.newDataBoard.bo.SettleBillBo;
import com.shands.mod.dao.model.req.UmMessageReq;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.util.DateUtil;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.message.consumer.ExcitationMessage;
import com.shands.mod.message.enums.MessageTypeEnum;
import com.shands.mod.message.service.UmMessagePushService;
import com.shands.mod.util.Tools;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description: 发送结算账单已生成消息给酒店
 * @Author: wj
 * @Date: 2024/10/25 11:03
 */
@Component
@Slf4j
public class SendHotelBillMsgJob {

  @Resource
  private ModUserDao modUserDao;
  @Resource
  private ExcitationMessage excitationMessage;
  @Autowired
  private UmMessagePushService umMessagePushService;
  @Autowired
  private UserInfoCommonService userInfoCommonService;
  @Autowired
  private SettleBillFileRecordsMapper settleBillFileRecordsMapper;
  @Autowired
  private ModHotelInfoDao modHotelInfoDao;

  @Autowired
  private AdsFinaDlEcoAppSettlementBillMMapper biSettlementBillMMapper;


  @Value("${els.h5url:https://m.kaiyuanhotels.com}")
  private String sxeH5Url;

  private static final String MSG_TITLE = "结算账单";
  private static final String MSG_CONTENT_FORMAT = "【账单通知】本期应结金额：%s元;酒店支付集团金额%s元";
  private static final String MSG_JOIN_HOTEL_BILL_H5_URL_FORMAT = "/sxe/statement?screen=full&month=%s";


  /**
   * 发送结算账单已生成消息给酒店
   * [ { "month": "2024-09", "hotelCode": "YJCXSA","hotelId":1 }, { "month": "2024-09","hotelCode": "YJCXSA","hotelId":1 } ]
   *
   * @param param
   * @return
   * @throws Exception
   */
  @XxlJob("sendHotelBillMsgJob")
  public ReturnT<String> sendHotelBillMsgJob(String param) throws Exception {
    log.info("任务开始: sendHotelBillMsgJob param= {}", param);
    long startTime = System.currentTimeMillis();
    final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name().toLowerCase();


    List<SettleBillBo> reqList;
    if (StringUtils.isEmpty(param)) {
      List<ModHotelInfo> hotelInfoList = modHotelInfoDao.findAllHotelList();
      String month = DateUtil.getLastMonth();
      reqList = hotelInfoList.stream().map(hotelInfo -> {
        SettleBillBo bo = new SettleBillBo();
        bo.setMonth(month);
        bo.setHotelCode(hotelInfo.getHotelCode());
        bo.setHotelId(hotelInfo.getHotelId());
        return bo;
      }).collect(Collectors.toList());
    } else {
      reqList = JSONArray.parseArray(param, SettleBillBo.class);
    }

    for (SettleBillBo billBo : reqList) {
      final String url = sxeH5Url + String.format(MSG_JOIN_HOTEL_BILL_H5_URL_FORMAT, billBo.getMonth());
      processBill(billBo, finalStatementCode, url);
    }

    long endTime = System.currentTimeMillis();
    log.info("任务结束: sendHotelBillMsgJob, 耗时: {} ms", (endTime - startTime));
    return ReturnT.SUCCESS;
  }

  private void processBill(SettleBillBo billBo, String finalStatementCode, String url) {

      String month = billBo.getMonth();
      String hotelCode = billBo.getHotelCode();
      Integer hotelId = billBo.getHotelId();

      // 校验是否已生成查询账单文件
      if (!isSettleBillFileExists(hotelCode, month)) {
        log.info("结算账单文件不存在 hotelCode: {} month: {}", hotelCode, month);
        return;
      }

      // 查询业主列表
      List<ModUser> users = getHotelUsers(hotelCode);
      if (CollectionUtils.isEmpty(users)) {
        log.warn("推送账单未找到酒店机构下关联员工 {}", hotelCode);
        return;
      }

      AdsFinaDlEcoAppSettlementBillMExample example = new AdsFinaDlEcoAppSettlementBillMExample();
      try {
        example.createCriteria().andHotelCodeEqualTo(hotelCode).andBizMonthEqualTo(DateUtils.strConverDate(month));
      } catch (ParseException e) {
        log.error("时间转换异常, month:{}", month);
        return;
      }
      List<AdsFinaDlEcoAppSettlementBillM> billMList = biSettlementBillMMapper.selectByExample(example);
      if (CollUtil.isEmpty(billMList)) {
        log.info("未查询到结算账单数据 hotelCode: {} month: {}", hotelCode, month);
        return;
      }
      AdsFinaDlEcoAppSettlementBillM bill = billMList.get(0);
      // 本期应结金额
      String total = Tools.formatAmountWithCommas(bill.getSettlementAmt());
      // 酒店支付集团_净额
      String payGroupNetFee = Tools.formatAmountWithCommas(bill.getPayGroupNetFee());

      // 账单通知内容 【账单通知】本期应结金额：1,200,000.09元;酒店支付集团金额110,000.06元
      String content = String.format(MSG_CONTENT_FORMAT, total, payGroupNetFee);

      // 发送消息给有权限的用户
      for (ModUser user : users) {
        try {
          sendBillMessage(user, hotelId, finalStatementCode, content, url);
        } catch (Exception e) {
          log.error("账单消息发送失败 {} {}", hotelCode, user.getId(), e);

        }
      }

  }

  private boolean isSettleBillFileExists(String hotelCode, String month) {
    List<SettleBillFileRecords> records = getSettleBillFileRecords(hotelCode, month);
    return CollectionUtils.isNotEmpty(records);
  }

  private SettleBillFileRecords getSettleBillFileRecord(String hotelCode, String month) {
    List<SettleBillFileRecords> records = getSettleBillFileRecords(hotelCode, month);
    return records.get(0);
  }

  private List<SettleBillFileRecords> getSettleBillFileRecords(String hotelCode, String month) {
    SettleBillFileRecordsExample example = new SettleBillFileRecordsExample();
    example.createCriteria()
        .andHotelCodeEqualTo(hotelCode)
        .andBizMonthEqualTo(month)
        .andDelFlagEqualTo(0)
        .andUserIdEqualTo(0);
    return settleBillFileRecordsMapper.selectByExample(example);
  }

  private List<ModUser> getHotelUsers(String hotelCode) {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryHotelInfoByCode(hotelCode);
    if (modHotelInfo == null) {
      return Collections.emptyList();
    }

    Integer ucCompanyId = modHotelInfo.getUcCompanyId();
    ModUser modUser = new ModUser();
    modUser.setUcCompanyId(ucCompanyId);
    modUser.setDeleted(0);
    modUser.setOutSource(0);
    return modUserDao.queryAll(modUser);
  }


  private void sendBillMessage(ModUser user, Integer hotelId, String finalStatementCode,
      String content, String url) {
    List<String> rightList = userInfoCommonService.getUserRights(user.getId(), hotelId,
        UserRightsTypeEnum.APP);
    if (CollectionUtils.isNotEmpty(rightList) && rightList.contains(finalStatementCode)) {
      // 保存消息发送记录
      int messageId = saveMessageLog(user, content, url);

      // 组装友盟推送消息体
      UmMessageReq umMessageReq = createUmMessageReq(user, content, url, messageId);

      // 调用友盟推送
      umMessagePushService.sendMessage(umMessageReq);
    }
  }

  private int saveMessageLog(ModUser user, String content, String url) {
    return excitationMessage.saveMessageLog(
        MessageTypeEnum.MESSAGE_TYPE_APP,
        MSG_TITLE,
        content,
        MessageTypeEnum.MESSAGE_TYPE_EXCITATION_H5.getTypeCode(),
        null,
        MessageTypeEnum.REJT_TYPE_STAFF,
        String.valueOf(user.getId()),
        url
    );
  }

  private UmMessageReq createUmMessageReq(ModUser user, String content, String url, int messageId) {
    return excitationMessage.getSendObject(
        user.getDeviceId(),
        user.getDeviceType(),
        content,
        MSG_TITLE,
        MessageTypeEnum.MESSAGE_TYPE_EXCITATION_H5.getTypeCode(),
        String.valueOf(messageId),
        null,
        url
    );
  }
}
