package com.shands.mod.main.service.statistics;

import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.statistics.req.BusinessDataDetailReq;
import com.shands.mod.dao.model.statistics.req.CommonStatisticReq;
import com.shands.mod.dao.model.statistics.vo.BaseStatisticsDataVo;
import com.shands.mod.dao.model.statistics.vo.BetterWoodMemberDataVo;
import com.shands.mod.dao.model.statistics.vo.BusinessDataDetailVo;
import com.shands.mod.dao.model.statistics.vo.RoomNightDetailVo;
import com.shands.mod.dao.model.statistics.vo.SellCardRankingVo;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 经营数据概况服务接口
 * @Author: mazhiyong
 */
public interface StatisticsDataService {

    /**
     * 获取经营数据概况
     * @param req 请求参数
     * @return 经营数据概况
     */
    BaseStatisticsDataVo getBriefBusinessData(CommonStatisticReq req);

    /**
     * 获取经营数据详情
     * @param req 请求参数
     * @return 经营数据详情
     */
    BusinessDataDetailVo getBusinessDataDetail(BusinessDataDetailReq req);

    /**
     * 获取已售间夜分布详情
     * @param req 请求参数
     * @return 已售间夜分布详情
     */
    RoomNightDetailVo getRoomNightDetailData(@Valid CommonStatisticReq req);

    /**
     * 获取会员数据详情
     * @param req 请求参数
     * @return 会员数据详情
     */
    BetterWoodMemberDataVo getBetterWoodMemberData(@Valid CommonStatisticReq req);

    /**
     * 获取门店售卡奖金排行榜
     * @param req 请求参数
     * @return 门店售卡奖金排行榜数据
     */
    SellCardRankingVo getSellCardRanking(@Valid CommonStatisticReq req);

    /**
     * 获取经营数据详情指标
     * @return 经营数据详情指标
     */
    List<HomeDataDetailsVo> getBusinessDataIndicator();
}
