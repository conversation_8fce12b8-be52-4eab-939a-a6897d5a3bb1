package com.shands.mod.main.service.statistics;

import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;

import java.util.List;

/**
 * @Description: 百达卡销售统计统计服务接口
 * @Author: mazhiyong
 */
public interface BdwCardStatisticService {
    /**
     * 查询百达卡销售相关数据
     * @param deptDataReq 请求参数
     * @return 百达卡销售相关数据
     */
    List<DeptDataVo> queryBdwCardDeptData(DeptDataReq deptDataReq);
}
