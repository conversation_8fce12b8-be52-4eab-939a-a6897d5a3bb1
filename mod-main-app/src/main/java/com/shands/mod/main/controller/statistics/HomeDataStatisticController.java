package com.shands.mod.main.controller.statistics;


import com.shands.mod.dao.model.statistics.vo.BillNotifyVo;
import com.shands.mod.main.service.statistics.HomeDataStatisticService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



@RestController
@RequestMapping("/homeData")
@Api(value = "首页数据统计", tags = "首页数据")
@Slf4j
public class HomeDataStatisticController {

    @Autowired
    private HomeDataStatisticService homeDataStatisticService;

    @ApiOperation(value = "账单通知")
    @GetMapping(value = "/billNotify")
    @ApiResponse(code = 0, message = "成功",response = BillNotifyVo.class)
    public ResultVO<BillNotifyVo> billNotify() {
        return ResultVO.success(homeDataStatisticService.queryBillNotifyLast());
    }
}
