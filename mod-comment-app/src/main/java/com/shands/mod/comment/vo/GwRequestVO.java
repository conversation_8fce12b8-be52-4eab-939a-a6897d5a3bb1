package com.shands.mod.comment.vo;

import java.io.Serializable;

/**
 * 官网请求VO
 *
 * <AUTHOR>
 */
public class GwRequestVO implements Serializable {
  private static final long serialVersionUID = -3708890448332782949L;
  /** GW接口header['shands'] */
  private String shands;
  /** GW接口header['appkey'] */
  private String appkey;
  /** GW接口header['timestamp'] */
  private String timestamp;
  /** GW接口header['token'] */
  private String token;
  /** GW接口header['v'] */
  private String v;
  /** GW接口header['authorization'] */
  private String authorization;
  /** body内容，包含pageNo页码(第一页是1)，pageSize每页记录数(默认10)，tradeType订单类型(默认0全部;1待付款;2待出行;3待评价) */
  private String body;

  public String getShands() {
    return shands;
  }

  public void setShands(String shands) {
    this.shands = shands;
  }

  public String getAppkey() {
    return appkey;
  }

  public void setAppkey(String appkey) {
    this.appkey = appkey;
  }

  public String getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(String timestamp) {
    this.timestamp = timestamp;
  }

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public String getV() {
    return v;
  }

  public void setV(String v) {
    this.v = v;
  }

  public String getAuthorization() {
    return authorization;
  }

  public void setAuthorization(String authorization) {
    this.authorization = authorization;
  }

  public String getBody() {
    return body;
  }

  public void setBody(String body) {
    this.body = body;
  }

  @Override
  public String toString() {
    return "GwRequestVO{"
        + "shands='"
        + shands
        + '\''
        + ", appkey='"
        + appkey
        + '\''
        + ", timestamp='"
        + timestamp
        + '\''
        + ", token='"
        + token
        + '\''
        + ", v='"
        + v
        + '\''
        + ", authorization='"
        + authorization
        + '\''
        + ", body='"
        + body
        + '\''
        + '}';
  }
}
