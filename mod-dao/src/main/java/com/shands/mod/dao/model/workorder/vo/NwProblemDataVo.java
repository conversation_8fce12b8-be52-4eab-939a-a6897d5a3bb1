package com.shands.mod.dao.model.workorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@ApiModel("问题分类统计")
public class NwProblemDataVo {

  @ApiModelProperty(value="酒店id",required = true)
  private int problemId;

  @ApiModelProperty(value="酒店名称",required = true)
  private String problemName;

  @ApiModelProperty(value="总数",required = true)
  private int total;

  @ApiModelProperty(value="完成数",required = true)
  private int completeNum;

  @ApiModelProperty(value="关闭数",required = true)
  private int closeNum;

  @ApiModelProperty(value="处理中数",required = true)
  private int processNum;

  @ApiModelProperty(value="超时数",required = true)
  private int outTimeNum;

  @ApiModelProperty(value="已处理数",required = true)
  private int inProcessNum;

  @ApiModelProperty(value="平均时间（秒）",required = true)
  private long avgTime;

  @ApiModelProperty(value="平均小时数",required = true)
  private long avgHour;

  @ApiModelProperty(value="平均分钟数",required = true)
  private long avgMinune;
}
