package com.shands.mod.dao.model.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 会员数据VO
 * @Author: mazhiyong
 */
@Data
@ApiModel("会员数据VO")
public class BetterWoodMemberDataVo {

    @ApiModelProperty(value = "APP消费会员数据")
    private BaseStatisticsDataVo appConsumeMemberData = new BaseStatisticsDataVo();

    @ApiModelProperty(value = "会员业绩归属数据")
    private BaseStatisticsDataVo memberPerformanceBelongData = new BaseStatisticsDataVo();

    @ApiModelProperty(value = "门店法宝数据")
    private BaseStatisticsDataVo hotelVoucherData = new BaseStatisticsDataVo();

    @ApiModelProperty(value = "百达卡销售数据")
    private BetterWoodCardSellDataVo betterWoodCardSellData = new BetterWoodCardSellDataVo();
}
