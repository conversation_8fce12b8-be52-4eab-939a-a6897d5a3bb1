package com.shands.mod.dao.model.newDataBoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3
 * @desc 首页总经理绩效看板返回vo
 */
@Data
@ApiModel(value = "首页总经理绩效看板返回vo")
public class ManagerHomeInfoData {

  @ApiModelProperty(value = "酒店编码")
  private String hotelCode;

  @ApiModelProperty(value = "酒店名称")
  private String hotelName;

  @ApiModelProperty(value = "当前进度")
  private String completion;

  @ApiModelProperty(value = "排名")
  private Integer sort;

  @ApiModelProperty(value = "进度")
  private Integer progress;

  @ApiModelProperty(value = "当前酒店")
  private Boolean isFlag = Boolean.FALSE;
}
