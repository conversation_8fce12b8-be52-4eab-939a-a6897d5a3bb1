package com.shands.mod.dao.mapper.crm;

import com.shands.mod.dao.model.crm.HsPosSort;
import com.shands.mod.dao.model.req.hs.crm.GetSortForMiniReq;
import com.shands.mod.dao.model.res.hs.crm.HsPosSortForMiniRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 菜类(HsPosSort)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-05-18 15:10:22
 */
public interface HsPosSortMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    HsPosSort queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<HsPosSort> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param hsPosSort 实例对象
     * @return 对象列表
     */
    List<HsPosSort> queryAll(HsPosSort hsPosSort);

    /**
     * 查询选择性
     *
     * @param hsPosSort hs pos排序
     * @return {@link List<HsPosSort>}
     */
    List<HsPosSort> queryBySelective(HsPosSort hsPosSort);

    /**
     * 新增数据
     *
     * @param hsPosSort 实例对象
     * @return 影响行数
     */
    int insert(HsPosSort hsPosSort);

    /**
     * 插入批入
     *
     * @param list 列表
     * @return int
     */
    int insertBatch(@Param("entities") List<HsPosSort> list);

    /**
     * 修改数据
     *
     * @param hsPosSort 实例对象
     * @return 影响行数
     */
    int update(HsPosSort hsPosSort);

    /**
     * 批量更新上下架
     *
     * @param list 列表
     * @return int
     */
    int updateStatusBatch(@Param("entities") List<HsPosSort> list);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

  /**
   * 菜类列表
   * @return
   */
    List<HsPosSortForMiniRes> sortByMini(GetSortForMiniReq req);

  /**
   * 删除所有
   */
  void deleteByCompanyId(@Param("companyId") Integer companyId, @Param("outletCode") String outletCode);

  /**
   * 逻辑删除
   *
   * @param companyId  公司标识
   * @param outletCode 营业点code
   */
  void deleteForlogic(@Param("companyId") Integer companyId, @Param("outletCode") String outletCode);

  List<HsPosSort> getSort(@Param("companyId")Integer companyId,@Param("noteCode")String noteCode);

  List<HsPosSort> queryProtectData(@Param("companyId") Integer companyId, @Param("outletCode") String outletCode);
}