package com.shands.mod.dao.model.newDataBoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("酒店运营信息展示 出参")
public class HotelOperationsVo {
  @ApiModelProperty("在营酒店数量")
  private Integer openNum;

  @ApiModelProperty("筹开酒店数量")
  private Integer raiseNum;

  @ApiModelProperty("筹建酒店数量")
  private Integer prepareNum;

  @ApiModelProperty("签约酒店数量")
  private Integer signNum;


  @ApiModelProperty("全部酒店数量")
  private Integer totalPercentGmNum=0;
  @ApiModelProperty("全部平均分")
  private Double totalPercentAverage;
  //10%
  @ApiModelProperty("前10%酒店数量")
  private Integer tenPercentGmNum=0;
  @ApiModelProperty("前10%平均分")
  private Double tenPercentAverage;
  //80%
  @ApiModelProperty("后80%酒店数量")
  private Integer eightyPercentGmNum;
  @ApiModelProperty("后80%平均分")
  private Double eightyPercentAverage;
  //最后10%
  @ApiModelProperty("最后10%酒店数量")
  private Integer lastPercentGmNum;
  @ApiModelProperty("最后10%平均分")
  private Double lastPercentAverage;


  @ApiModelProperty("筹开期酒店数量")
  private Integer raiseHotelNum;
  @ApiModelProperty("筹开期酒店平均分")
  private Double raiseAverage;
  @ApiModelProperty("业务日期")
  private String bizDate;

  @ApiModelProperty("事业部code")
  private String divisionCode;

  @ApiModelProperty("url")
  private String url="/sxe/itemAnalyse?ownerShip=";

  @ApiModelProperty("事业部名称")
  private String divisionName;

  @ApiModelProperty("特许经营酒店数量")
  private Integer franchiseHotelNum;
}
