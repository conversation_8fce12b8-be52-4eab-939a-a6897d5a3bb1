package com.shands.mod.dao.model.statistics.vo;

import com.shands.mod.dao.model.statistics.dto.EmployeeRankingItem;
import com.shands.mod.dao.model.statistics.dto.HotelRankingItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 门店售卡奖金排行榜VO
 * @Author: mazhiyong
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("门店售卡奖金排行榜VO")
public class SellCardRankingVo {

    @ApiModelProperty("指标标题")
    private String title;

    @ApiModelProperty("是否展示该数据")
    private Boolean showFlag;

    @ApiModelProperty("门店排行榜")
    private List<HotelRankingItem> hotelRanking;

    @ApiModelProperty("员工排行榜")
    private List<EmployeeRankingItem> employeeRanking;

}
