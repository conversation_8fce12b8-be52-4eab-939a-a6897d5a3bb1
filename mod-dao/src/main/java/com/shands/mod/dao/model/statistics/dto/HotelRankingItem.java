package com.shands.mod.dao.model.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店排行榜项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelRankingItem {
    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("百达卡数量")
    private Long cardNum;

    @ApiModelProperty("百达卡奖金(分)")
    private Long cardBonus;
}