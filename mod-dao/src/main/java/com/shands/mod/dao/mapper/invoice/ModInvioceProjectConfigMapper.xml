<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.invoice.ModInvioceProjectConfigMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.invoice.ModInvioceProjectConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="consume_code" property="consumeCode" jdbcType="VARCHAR" />
    <result column="consume_item" property="consumeItem" jdbcType="VARCHAR" />
    <result column="tax_id" property="taxId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="GROUP_ID" property="groupId" jdbcType="INTEGER" />
    <result column="VERSION" property="version" jdbcType="INTEGER" />
    <result column="DELETED" property="deleted" jdbcType="TINYINT" />
    <result column="CREATE_USER" property="createUser" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, consume_code, consume_item, tax_id, COMPANY_ID, GROUP_ID, VERSION, DELETED, CREATE_USER, 
    CREATE_TIME, UPDATE_USER, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from mod_invoice_project_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mod_invoice_project_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.invoice.ModInvioceProjectConfig"  keyProperty="id"
    useGeneratedKeys="true">
    insert into mod_invoice_project_config (id, consume_code, consume_item, 
      tax_id, COMPANY_ID, GROUP_ID, 
      VERSION, DELETED, CREATE_USER, 
      CREATE_TIME, UPDATE_USER, UPDATE_TIME
      )
    values (#{id,jdbcType=INTEGER}, #{consumeCode,jdbcType=VARCHAR}, #{consumeItem,jdbcType=VARCHAR}, 
      #{taxId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createUser,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.invoice.ModInvioceProjectConfig"  keyProperty="id"
    useGeneratedKeys="true" >
    insert into mod_invoice_project_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="consumeCode != null" >
        consume_code,
      </if>
      <if test="consumeItem != null" >
        consume_item,
      </if>
      <if test="taxId != null" >
        tax_id,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="groupId != null" >
        GROUP_ID,
      </if>
      <if test="version != null" >
        VERSION,
      </if>
      <if test="deleted != null" >
        DELETED,
      </if>
      <if test="createUser != null" >
        CREATE_USER,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateUser != null" >
        UPDATE_USER,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="consumeCode != null" >
        #{consumeCode,jdbcType=VARCHAR},
      </if>
      <if test="consumeItem != null" >
        #{consumeItem,jdbcType=VARCHAR},
      </if>
      <if test="taxId != null" >
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null" >
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.invoice.ModInvioceProjectConfig" >
    update mod_invoice_project_config
    <set >
      <if test="consumeCode != null" >
        consume_code = #{consumeCode,jdbcType=VARCHAR},
      </if>
      <if test="consumeItem != null" >
        consume_item = #{consumeItem,jdbcType=VARCHAR},
      </if>
      <if test="taxId != null" >
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null" >
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        DELETED = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null" >
        CREATE_USER = #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.invoice.ModInvioceProjectConfig" >
    update mod_invoice_project_config
    set consume_code = #{consumeCode,jdbcType=VARCHAR},
      consume_item = #{consumeItem,jdbcType=VARCHAR},
      tax_id = #{taxId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      GROUP_ID = #{groupId,jdbcType=INTEGER},
      VERSION = #{version,jdbcType=INTEGER},
      DELETED = #{deleted,jdbcType=TINYINT},
      CREATE_USER = #{createUser,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--列表-->
  <select id="configList" resultType="com.shands.mod.dao.model.res.invoice.InvoiceProjectConfigRes">
    SELECT
    a.id as id,
	a.`consume_code` consumeCode,
	a.`consume_item` consumeItem,
	b.`tax_type_code` taxTypeCode,
	b.`tax_type_name` taxTypeName,
	b.`invoice_type` invoiceType,
	a.tax_id taxId
FROM
	mod_invoice_project_config a
	LEFT JOIN mod_tax_rate b ON a.tax_id = b.id
	where a.COMPANY_ID = #{companyId}
	and a.deleted=0
	and b.deleted=0
  </select>

  <!--根据税收分类id查找是否有试用-->
  <select id="countTax" resultType="java.lang.Integer">
    select count(*) from mod_invoice_project_config
    where tax_id=#{id} and deleted=0
  </select>

  <!--详情-->
  <select id="configDetail" resultType="com.shands.mod.dao.model.res.invoice.InvoiceProjectConfigRes">
     SELECT
    a.id as id,
	a.`consume_code` consumeCode,
	a.`consume_item` consumeItem,
	b.`tax_type_code` taxTypeCode,
	b.`tax_type_name` taxTypeName,
	b.`invoice_type` invoiceType,
	b.policy  policy,
	b.tax_rate taxRate,
	a.tax_id taxId
FROM
	mod_invoice_project_config a
	LEFT JOIN mod_tax_rate b ON a.tax_id = b.id
	where a.deleted=0
	and b.deleted=0
	and a.id=#{id}
  </select>

  <!--查看消费代码是否有相同的-->
  <select id="countSame" resultType="java.lang.Integer">
    select count(*) from mod_invoice_project_config where
    consume_code=#{consumeCode}
    and company_id = #{companyId}
    and deleted=0
    <if test="id !=null">
      and id != #{id}
    </if>
  </select>
<select id="codeByType" resultType="com.shands.mod.dao.model.res.invoice.OpenInvoiceProjectByInvoiceTypeRes">
  SELECT
	a.id as id,
	a.consume_code as consumeCode,
	a.consume_item as consumeItem,
  b.tax_type_name as xmmc
FROM
	mod_invoice_project_config a
	LEFT JOIN mod_tax_rate b ON a.tax_id = b.id
WHERE
  a.company_id = #{companyId}
  and b.company_id = #{companyId}
  and a.deleted = 0
  and b.deleted = 0
  and a.consume_code = #{consumeCode}
  <if test="invoiceType !=null">
    and FIND_IN_SET( #{invoiceType}, b.invoice_type )
  </if>
</select>
  <!--根据发票类型 获得开票项-->
  <select id="getByType" resultType="com.shands.mod.dao.model.res.invoice.OpenInvoiceProjectByInvoiceTypeRes">
    SELECT
    a.id as id,
    a.consume_code as consumeCode,
    a.consume_item as consumeItem,
    b.tax_type_name as xmmc
    FROM
    mod_invoice_project_config a
    LEFT JOIN mod_tax_rate b ON a.tax_id = b.id
    WHERE
    a.company_id = #{companyId}
    and b.company_id = #{companyId}
    and a.deleted = 0
    and b.deleted = 0
    <if test="invoiceType !=null">
      and FIND_IN_SET( #{invoiceType}, b.invoice_type )
    </if>
  </select>

</mapper>