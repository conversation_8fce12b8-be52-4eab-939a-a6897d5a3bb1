<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.homerevision.ModSysMessageRoleDao">

  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.homerevision.ModSysMessageRole">
    <!--@Table mod_sys_message_role-->
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="messageId" column="message_id" jdbcType="INTEGER"/>
    <result property="roleCode" column="role_code" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="createUser" column="create_user" jdbcType="INTEGER"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
  </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="BaseResultMap">
        select
          id, message_id, role_code, create_time, create_user, update_time, update_user, remark
        from mod_sys_message_role
        where id = #{id}
    </select>

  <select id="queryRoles" resultType="java.lang.String">
        select role_code
        from mod_sys_message_role
        where message_id = #{messageId}
    </select>

  <!--查询指定行数据-->
  <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, message_id, role_code, create_time, create_user, update_time, update_user, remark
        from mod_sys_message_role
        limit #{offset}, #{limit}
    </select>


  <!--通过实体作为筛选条件查询-->
  <select id="queryAll" resultMap="BaseResultMap">
    select
    id, message_id, role_code, create_time, create_user, update_time, update_user, remark
    from mod_sys_message_role
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="messageId != null">
        and message_id = #{messageId}
      </if>
      <if test="roleCode != null and roleCode != ''">
        and role_code = #{roleCode}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
    </where>
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_sys_message_role(message_id, role_code, create_time, create_user, update_time, update_user, remark)
        values (#{messageId}, #{roleCode}, #{createTime}, #{createUser}, #{updateTime}, #{updateUser}, #{remark})
    </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update mod_sys_message_role
    <set>
      <if test="messageId != null">
        message_id = #{messageId},
      </if>
      <if test="roleCode != null and roleCode != ''">
        role_code = #{roleCode},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="createUser != null">
        create_user = #{createUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
        delete from mod_sys_message_role where id = #{id}
    </delete>

  <!--通过主键删除-->
  <delete id="deleteByMessageId">
        delete from mod_sys_message_role where message_id = #{messageId}
    </delete>

</mapper>