package com.shands.mod.dao.model.workorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@ApiModel("总览")
public class NwOverviewVo {

  @ApiModelProperty(value="总数")
  private int total;

  @ApiModelProperty(value="完成数")
  private int completeNum;

  @ApiModelProperty(value="完成率",required = true)
  private Double completeRate;

  @ApiModelProperty(value="解决数",required = true)
  private int solveNum;

  @ApiModelProperty(value="解决率",required = true)
  private Double solveRate;

  @ApiModelProperty(value="超时数",required = true)
  private int outTimeNum;

  @ApiModelProperty(value="超时率",required = true)
  private Double outTimeRate;

  @ApiModelProperty(value="总时间（秒）",required = true)
  private long totalTime;

  @ApiModelProperty(value="平均时间（秒）",required = true)
  private long avgTime;

  @ApiModelProperty(value="总小时数",required = true)
  private long totalHour;

  @ApiModelProperty(value="总分钟数",required = true)
  private long totalMinune;

  @ApiModelProperty(value="平均小时数",required = true)
  private long avgHour;

  @ApiModelProperty(value="平均分钟数",required = true)
  private long avgMinune;
}
