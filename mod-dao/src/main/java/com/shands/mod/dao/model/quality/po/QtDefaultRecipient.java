package com.shands.mod.dao.model.quality.po;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;
/**
 * 部门整改任务默认接收人表(QtDefaultRecipient)实体类
 *
 * <AUTHOR>
 * @since 2022-04-15 14:41:24
 */
@Data
public class QtDefaultRecipient implements Serializable {
  private static final long serialVersionUID = 544142782980757940L;

  private Integer id;

  private Date createTime;

  private Date updateTime;
  /**
   * 备注
   */
  private String remark;

  private Integer createUser;

  private Integer updateUser;

  private Boolean ifDelete;
  /**
   * 默认任务时长
   */
  private Integer defaultTaskTime;
  /**
   * 默认优先级
   */
  private String defaultPriority;
  /**
   * 酒店id
   */
  private Integer hotelId;

}
