package com.shands.mod.dao.model.res;

import java.util.List;

/**
 * <Description> 部门员工树<br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/11/23 3:54 下午 <br>
 * @see com.shands.mod.dao.model.res <br>
 */

public class TreeNodeNew {

  private Integer id;

  private String name;

  private List<TreeNodeNew> childs; // TODO 自己调用自己

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<TreeNodeNew> getChilds() {
    return childs;
  }

  public void setChilds(List<TreeNodeNew> childs) {
    this.childs = childs;
  }
}
