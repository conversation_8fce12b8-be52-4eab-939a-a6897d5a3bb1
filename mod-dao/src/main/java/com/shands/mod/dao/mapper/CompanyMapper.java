package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.Company;
import com.shands.mod.dao.model.hs.PmsGreencloud;
import com.shands.mod.dao.model.req.*;
import com.shands.mod.dao.model.res.*;
import com.shands.mod.dao.model.v0701.vo.BreakfastRemindVo;
import java.awt.Component;
import com.shands.mod.dao.model.v0701.vo.CheckInCompanyVo;
import com.shands.mod.dao.model.v0701.vo.CompanyPmsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CompanyMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(Company record);

  int insertSelective(Company record);

  Company selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(Company record);

  int updateByPrimaryKeyWithBLOBs(Company record);

  int updateByPrimaryKey(Company record);

  /**
   * 根据集团/门店id获取所有信息
   *
   * @param id id
   * @return 实体
   */
  CompanyEditInfoResAndReq getAllById(@Param("id") Integer id);

  /**
   * 根据id更新企业信息
   *
   * @param req 请求参数
   * @return boolean
   */
  boolean updateInfoById(@Param("model") CompanyEditInfoResAndReq req);

  /**
   * 增加下属机构
   *
   * @param child 机构信息
   * @return boolean
   */
  boolean addChildCompany(@Param("child") CompanyAddAndUpdateChildReq child);

  /**
   * 根据id修改机构信息
   *
   * @param child 机构信息
   * @return boolean
   */
  boolean updateCompany(@Param("child") CompanyAddAndUpdateChildReq child);

  /**
   * 获取下属机构列表
   *
   * @param groupId 集团id
   * @return list
   */
  List<CompanyGetChildInfoListRes> getInfoList(@Param("model") CompanyGetChildInfoListReq groupId);

  /**
   * 根据手机号获取用户姓名邮箱
   *
   * @param phone 手机号
   * @return obj
   */
  CompanyGetUserInfoRes getUserInfo(String phone);

  /**
   * 根据请求参数获取列表,查询所有门店信息
   *
   * @param req 请求参数
   * @return list
   */
  List<CustomerGetListRes> getCustomerList(@Param("model") CustomerGetListReq req);

  /**
   * 增加集团信息
   *
   * @param req 请求参数
   * @return boolean
   */
  boolean addGroup(@Param("model") CustomerAddAndUpdateReq req);

  /**
   * 切换门店状态
   *
   * @param companyId companyId
   * @param userId userId
   * @return boolean
   */
  boolean switchStatus(@Param("companyId") Integer companyId, @Param("userId") Integer userId);

  /**
   * 根据id获取门店详情
   *
   * @param companyId 门店id
   * @return 一级门店/机构
   */
  CustomerGetInfoRes getInfo(@Param("companyId") Integer companyId);

  /**
   * 获取所有一级机构
   *
   * @return list
   */
  List<CustomerGetGroupIdsRes> getGroupIds();

  /**
   * 移除企业的管理员
   *
   * @param companyId 企业id
   * @return boolean
   */
  boolean updateAdminByCompanyId(@Param("companyId") int companyId);

  /**
   * 获取企业设置信息
   *
   * @param companyId 企业id
   * @return string
   */
  String getCompanySetting(@Param("companyId") int companyId);

  int updateSMSById(Map map);

  int updateDateM(Map map);

  int updateDateY(Map map);

  /**
   * 更新企业短信使用量(+1)
   *
   * @param companyId 企业ID
   * @return
   */
  int addSmsUsed(@Param("companyId") int companyId);

  /**
   * 通过酒店id获取酒店名称
   *
   * @param companyId id
   * @return 名称
   */
  String findCompanyNameByCompanyId(@Param("companyId") Integer companyId);

  CompanyDistance getDistance(DistanceReq req);

  List<Company> getAllCompany();

  /**
   * 查询需要执行早餐提醒的酒店信息
   * @return
   */
  List<BreakfastRemindVo> getBreakfasts();

  Company getInvoice(Integer companyId);

  Company getTaxNumber(Integer compayId);

  List<Company> weatherForCompany(@Param("companyId") Integer companyId);

  List<String> selectForOnlie();

  CheckInCompanyVo selectByHotelCode(String hotelCode);

  /**
   * 查询酒店及pms数据
   *
   * @param record 记录
   * @return {@link List<CompanyPmsVo>}
   */
  List<CompanyPmsVo> selectCompanyAndPms(PmsGreencloud record);
}
