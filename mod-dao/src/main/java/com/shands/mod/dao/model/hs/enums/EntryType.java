package com.shands.mod.dao.model.hs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntryType {
  companyQR(1, "酒店二维码", "companyQR"),
  roomQR(2, "房间二维码", "roomQR"),
  QRE(3, "扫码", "QRE"),
  officialE(4, "官网进入", "officialE"),
  searchE(5, "搜索小程序进入", "searchE"),
  noteE(6,"入住推送进入","noteE"),
  ;

  private final int code;
  private final String cnName;
  private final String enName;

  public static EntryType int2Priority(int i) {
    switch (i) {
      case 1:
        return companyQR;
      case 2:
        return roomQR;
      case 3:
        return QRE;
      case 4:
        return officialE;
      case 5:
        return searchE;
      case 6:
        return noteE;
    }
    return null;
  }
}
