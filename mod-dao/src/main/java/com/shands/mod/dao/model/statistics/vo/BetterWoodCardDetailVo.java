package com.shands.mod.dao.model.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 百达卡销售详情VO
 * @Author: mazhiyong
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("百达卡销售详情VO")
public class BetterWoodCardDetailVo {

    @ApiModelProperty("百达卡名称")
    private String cardName;

    @ApiModelProperty("百达卡销售数量")
    private Integer cardNum;

    @ApiModelProperty("百达卡奖金(分)")
    private Long cardBonus;
}
