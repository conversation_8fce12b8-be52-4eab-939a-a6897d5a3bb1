package com.shands.mod.dao.mapper.datarevision;

import com.shands.mod.dao.model.datarevision.po.DwsMemberGradeDay;
import com.shands.mod.dao.model.datarevision.vo.GeneralDrawingVo;
import com.shands.mod.dao.model.datarevision.vo.MemberGradeScaleVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 会员发展表(DwsMemberGradeDay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-02 14:12:59
 */
public interface DwsMemberGradeDayMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DwsMemberGradeDay queryById(Integer id);

    /**
     * 统计总行数
     *
     * @param dwsMemberGradeDay 查询条件
     * @return 总行数
     */
    long count(DwsMemberGradeDay dwsMemberGradeDay);

    /**
     * 新增数据
     *
     * @param dwsMemberGradeDay 实例对象
     * @return 影响行数
     */
    int insert(DwsMemberGradeDay dwsMemberGradeDay);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<DwsMemberGradeDay> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DwsMemberGradeDay> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<DwsMemberGradeDay> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<DwsMemberGradeDay> entities);

    /**
     * 修改数据
     *
     * @param dwsMemberGradeDay 实例对象
     * @return 影响行数
     */
    int update(DwsMemberGradeDay dwsMemberGradeDay);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

  /**
   * 百达星系会员规模
   * @return
   */
  List<GeneralDrawingVo> selectMemberScaleOfBdw();

  /**
   * 会员等级分布查询
   * @return
   */
    List<MemberGradeScaleVo> selectMemberGradeScale();
}