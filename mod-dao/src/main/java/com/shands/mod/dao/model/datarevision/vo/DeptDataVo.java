package com.shands.mod.dao.model.datarevision.vo;

import com.shands.mod.dao.util.ThousandSeparatorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/5/23 14:33
 */
@Data
@ApiModel("部门数据报表")
public class DeptDataVo {

  @ApiModelProperty("名称")
  private String key;
  private String deptName;

  @ApiModelProperty("数据")
  private BigDecimal value;

  @ApiModelProperty("千分位数据")
  private String valueStr;

  @ApiModelProperty("第一个数据")
  private String firstValue;

  @ApiModelProperty("第二个数据")
  private String secondValue;

  @ApiModelProperty("第一个数据(数字)")
  private BigDecimal firstValueNum;

  @ApiModelProperty("第二个数据(数字)")
  private BigDecimal secondValueNum;

  private List<DeptDataVo> child;

  public DeptDataVo(){}

  public String getValueStr() {
    valueStr = ThousandSeparatorUtil.format(value);
    return valueStr;
  }
}
