package com.shands.mod.dao.mapper.syncuc;

import com.shands.mod.dao.model.syncuc.HsDutifulArea;
import com.shands.mod.dao.model.syncuc.HsDutifulService;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户服务配置信息(HsDutifulService)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-02 00:01:13
 */
public interface HsDutifulServiceDao {

  /**
   * 通过ID查询单条数据
   *
   * @param id 主键
   * @return 实例对象
   */
  HsDutifulService queryById(Integer id);

  /**
   * 查询指定行数据
   *
   * @param offset 查询起始位置
   * @param limit  查询条数
   * @return 对象列表
   */
  List<HsDutifulService> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


  /**
   * 通过实体作为筛选条件查询
   *
   * @param hsDutifulService 实例对象
   * @return 对象列表
   */
  List<HsDutifulService> queryAll(HsDutifulService hsDutifulService);

  /**
   * 新增数据
   *
   * @param hsDutifulService 实例对象
   * @return 影响行数
   */
  int insert(HsDutifulService hsDutifulService);

  int insertBatch(@Param("entities") List<HsDutifulService> entities);

  /**
   * 修改数据
   *
   * @param hsDutifulService 实例对象
   * @return 影响行数
   */
  int update(HsDutifulService hsDutifulService);

  /**
   * 通过主键删除数据
   *
   * @param id 主键
   * @return 影响行数
   */
  int deleteById(Integer id);

}